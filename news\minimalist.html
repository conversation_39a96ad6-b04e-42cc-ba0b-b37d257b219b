<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高度差网络-项目展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #fafafa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 80px 40px;
        }

        .header {
            text-align: center;
            margin-bottom: 120px;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 300;
            color: #222;
            margin-bottom: 20px;
            letter-spacing: -1px;
        }

        .header p {
            font-size: 1.1rem;
            color: #666;
            font-weight: 300;
        }

        .projects {
            display: grid;
            gap: 80px;
        }

        .project {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 60px;
            align-items: center;
            padding: 60px 0;
            border-bottom: 1px solid #eee;
        }

        .project:last-child {
            border-bottom: none;
        }

        .project:nth-child(even) {
            grid-template-columns: 2fr 1fr;
        }

        .project:nth-child(even) .project-info {
            order: 2;
        }

        .project:nth-child(even) .project-visual {
            order: 1;
        }

        .project-visual {
            height: 300px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            transition: transform 0.3s ease;
        }

        .project-visual:hover {
            transform: translateY(-5px);
        }

        .project-info h3 {
            font-size: 2rem;
            font-weight: 400;
            color: #222;
            margin-bottom: 20px;
        }

        .project-info p {
            font-size: 1rem;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.8;
        }

        .project-meta {
            display: flex;
            gap: 40px;
            margin-bottom: 30px;
        }

        .meta-item {
            text-align: center;
        }

        .meta-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #222;
            display: block;
        }

        .meta-label {
            font-size: 0.8rem;
            color: #999;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .project-actions {
            display: flex;
            gap: 20px;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            background: none;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            font-family: inherit;
        }

        .btn-primary {
            background: #222;
            color: #fff;
            border-radius: 4px;
        }

        .btn-primary:hover {
            background: #000;
            transform: translateY(-2px);
        }

        .btn-secondary {
            color: #666;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .btn-secondary:hover {
            color: #222;
            border-color: #222;
        }

        .floating-nav {
            position: fixed;
            top: 40px;
            right: 40px;
            display: flex;
            gap: 30px;
            z-index: 100;
        }

        .nav-item {
            color: #999;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .nav-item:hover {
            color: #222;
        }

        @media (max-width: 768px) {
            .container {
                padding: 40px 20px;
            }
            
            .header {
                margin-bottom: 60px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .project {
                grid-template-columns: 1fr !important;
                gap: 30px;
                padding: 40px 0;
            }
            
            .project:nth-child(even) .project-info {
                order: 1;
            }
            
            .project:nth-child(even) .project-visual {
                order: 2;
            }
            
            .project-meta {
                gap: 20px;
            }
            
            .floating-nav {
                display: none;
            }
        }
    </style>
</head>
<body>
    <nav class="floating-nav">
        <a href="https://www.gaodux.com" class="nav-item" target="_blank">访问官网</a>
        <a href="https://bbs.gaodux.com/" class="nav-item" target="_blank">访问论坛</a>
        <a href="https://bbs.gaodux.com/thread/48" class="nav-item" target="_blank">联系我们</a>
    </nav>

    <div class="container">
        <header class="header">
            <h1>项目展示</h1>
            <p>助力跨境进出口企业数字化转型升级</p>
        </header>

        <div class="projects">
            <div class="project">
                <div class="project-visual">
                    <img src="./images/1.jpg" alt="" style="width: 350px; border-radius: 20px;"></img>
                </div>
                <div class="project-info">
                    <h3>进口跨境电商系统</h3>
                    <p>智能零售+跨境电商商城系统，支持多种跨境贸易模式，提供一站式跨境解决方案，助力企业高效布局全球市场，打造私域流量增长新引擎。</p>
                    <div class="project-meta">
                        <div class="meta-item">
                            <span class="meta-value">v3.0</span>
                            <span class="meta-label">版本</span>
                        </div>
                    </div>
                    <div class="project-actions">
                        <a href="https://www.gaodux.com/shop/" class="btn btn-primary" target="_blank">PC端演示</a>
                        <a href="#" class="btn btn-secondary">微信小程序</a>
                        <!-- 需要鼠标移动到这里的时候显示微信小程序图片 -->
                         <div class="wx-qrcode">
                            <img src="./images/wx.png" alt="">
                        </div>
                    </div>
                </div>
            </div>

            <div class="project">
                <div class="project-visual">📋</div>
                <div class="project-info">
                    <h3>Workspace</h3>
                    <p>A clean and intuitive office management system that streamlines workflows and enhances team collaboration. Designed for modern distributed teams.</p>
                    <div class="project-meta">
                        <div class="meta-item">
                            <span class="meta-value">156</span>
                            <span class="meta-label">Teams</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-value">85%</span>
                            <span class="meta-label">Efficiency</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-value">24/7</span>
                            <span class="meta-label">Support</span>
                        </div>
                    </div>
                    <div class="project-actions">
                        <a href="#" class="btn btn-primary">Try Demo</a>
                        <a href="#" class="btn btn-secondary">Learn More</a>
                    </div>
                </div>
            </div>

            <div class="project">
                <div class="project-visual">🎓</div>
                <div class="project-info">
                    <h3>Learning Hub</h3>
                    <p>An elegant educational platform that makes learning accessible and engaging. Features adaptive content delivery and progress tracking.</p>
                    <div class="project-meta">
                        <div class="meta-item">
                            <span class="meta-value">8.9K</span>
                            <span class="meta-label">Students</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-value">94%</span>
                            <span class="meta-label">Completion</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-value">4.8</span>
                            <span class="meta-label">Score</span>
                        </div>
                    </div>
                    <div class="project-actions">
                        <a href="#" class="btn btn-primary">Explore</a>
                        <a href="#" class="btn btn-secondary">Features</a>
                    </div>
                </div>
            </div>

            <div class="project">
                <div class="project-visual">📊</div>
                <div class="project-info">
                    <h3>Analytics Suite</h3>
                    <p>A powerful yet simple data visualization platform that transforms complex datasets into actionable insights. Built for decision makers.</p>
                    <div class="project-meta">
                        <div class="meta-item">
                            <span class="meta-value">1.2M</span>
                            <span class="meta-label">Data Points</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-value">99.9%</span>
                            <span class="meta-label">Accuracy</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-value">Real-time</span>
                            <span class="meta-label">Updates</span>
                        </div>
                    </div>
                    <div class="project-actions">
                        <a href="#" class="btn btn-primary">View Dashboard</a>
                        <a href="#" class="btn btn-secondary">Documentation</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 平滑滚动效果
        document.addEventListener('DOMContentLoaded', function() {
            const projects = document.querySelectorAll('.project');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, { threshold: 0.1 });

            projects.forEach(project => {
                project.style.opacity = '0';
                project.style.transform = 'translateY(30px)';
                project.style.transition = 'all 0.6s ease';
                observer.observe(project);
            });
        });
    </script>
</body>
</html>
