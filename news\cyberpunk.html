<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CYBER PROJECTS - 赛博朋克项目展示</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', monospace;
            background: #0a0a0a;
            color: #00ff41;
            overflow-x: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                linear-gradient(90deg, transparent 98%, #00ff41 100%),
                linear-gradient(0deg, transparent 98%, #00ff41 100%);
            background-size: 50px 50px;
            opacity: 0.1;
            pointer-events: none;
            animation: gridMove 20s linear infinite;
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        .glitch {
            position: relative;
            animation: glitch 2s infinite;
        }

        @keyframes glitch {
            0%, 90%, 100% { transform: translate(0); }
            10% { transform: translate(-2px, 2px); }
            20% { transform: translate(2px, -2px); }
            30% { transform: translate(-2px, -2px); }
            40% { transform: translate(2px, 2px); }
            50% { transform: translate(-2px, 2px); }
            60% { transform: translate(2px, -2px); }
            70% { transform: translate(-2px, -2px); }
            80% { transform: translate(2px, 2px); }
        }

        .header {
            text-align: center;
            padding: 60px 20px;
            position: relative;
        }

        .header h1 {
            font-size: 4rem;
            font-weight: 900;
            text-transform: uppercase;
            letter-spacing: 5px;
            text-shadow: 
                0 0 10px #00ff41,
                0 0 20px #00ff41,
                0 0 40px #00ff41;
            margin-bottom: 20px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.8;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            padding: 40px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .project-card {
            background: linear-gradient(135deg, #1a1a1a, #0d0d0d);
            border: 2px solid #00ff41;
            border-radius: 10px;
            padding: 30px;
            position: relative;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .project-card::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #00ff41, #ff0080, #00ff41);
            border-radius: 10px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .project-card:hover::before {
            opacity: 1;
        }

        .project-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 255, 65, 0.3);
        }

        .project-title {
            font-size: 1.5rem;
            font-weight: 700;
            text-transform: uppercase;
            margin-bottom: 15px;
            color: #00ff41;
        }

        .project-desc {
            font-size: 0.9rem;
            line-height: 1.6;
            margin-bottom: 20px;
            opacity: 0.8;
        }

        .project-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 25px;
        }

        .stat-item {
            background: rgba(0, 255, 65, 0.1);
            padding: 10px;
            border-radius: 5px;
            border: 1px solid rgba(0, 255, 65, 0.3);
        }

        .stat-label {
            font-size: 0.7rem;
            opacity: 0.6;
            text-transform: uppercase;
        }

        .stat-value {
            font-size: 1.1rem;
            font-weight: 700;
            color: #00ff41;
        }

        .project-actions {
            display: flex;
            gap: 10px;
        }

        .cyber-btn {
            flex: 1;
            padding: 12px 20px;
            background: transparent;
            border: 2px solid #00ff41;
            color: #00ff41;
            text-transform: uppercase;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .cyber-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, #00ff41, transparent);
            transition: left 0.5s;
        }

        .cyber-btn:hover::before {
            left: 100%;
        }

        .cyber-btn:hover {
            background: #00ff41;
            color: #000;
            box-shadow: 0 0 20px #00ff41;
        }

        .terminal {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 300px;
            height: 150px;
            background: rgba(0, 0, 0, 0.9);
            border: 1px solid #00ff41;
            border-radius: 5px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            overflow: hidden;
        }

        .terminal-line {
            opacity: 0;
            animation: typewriter 0.5s ease forwards;
        }

        @keyframes typewriter {
            to { opacity: 1; }
        }

        @media (max-width: 768px) {
            .header h1 { font-size: 2.5rem; }
            .projects-grid { 
                grid-template-columns: 1fr;
                padding: 20px;
            }
            .terminal { display: none; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="glitch">CYBER PROJECTS</h1>
        <p>// 未来科技项目展示终端 //</p>
    </div>

    <div class="projects-grid">
        <div class="project-card">
            <h3 class="project-title">NEURAL COMMERCE</h3>
            <p class="project-desc">基于神经网络的智能商务平台，采用量子加密技术保护用户数据，实现毫秒级交易处理。</p>
            <div class="project-stats">
                <div class="stat-item">
                    <div class="stat-label">USERS</div>
                    <div class="stat-value">2,847</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">UPTIME</div>
                    <div class="stat-value">99.9%</div>
                </div>
            </div>
            <div class="project-actions">
                <button class="cyber-btn">ACCESS</button>
                <button class="cyber-btn">ADMIN</button>
            </div>
        </div>

        <div class="project-card">
            <h3 class="project-title">MATRIX OFFICE</h3>
            <p class="project-desc">虚拟现实办公系统，支持全息会议和脑机接口操作，让远程办公进入新纪元。</p>
            <div class="project-stats">
                <div class="stat-item">
                    <div class="stat-label">NODES</div>
                    <div class="stat-value">156</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">EFFICIENCY</div>
                    <div class="stat-value">+340%</div>
                </div>
            </div>
            <div class="project-actions">
                <button class="cyber-btn">ENTER</button>
                <button class="cyber-btn">CONTROL</button>
            </div>
        </div>

        <div class="project-card">
            <h3 class="project-title">QUANTUM EDU</h3>
            <p class="project-desc">量子教育平台，利用时空折叠技术实现知识的瞬间传输，重新定义学习体验。</p>
            <div class="project-stats">
                <div class="stat-item">
                    <div class="stat-label">STUDENTS</div>
                    <div class="stat-value">8,924</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">KNOWLEDGE</div>
                    <div class="stat-value">∞</div>
                </div>
            </div>
            <div class="project-actions">
                <button class="cyber-btn">LEARN</button>
                <button class="cyber-btn">TEACH</button>
            </div>
        </div>

        <div class="project-card">
            <h3 class="project-title">DATA NEXUS</h3>
            <p class="project-desc">超维数据分析中心，能够预测未来趋势并提供多元宇宙级别的商业洞察。</p>
            <div class="project-stats">
                <div class="stat-item">
                    <div class="stat-label">DIMENSIONS</div>
                    <div class="stat-value">11</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">ACCURACY</div>
                    <div class="stat-value">99.99%</div>
                </div>
            </div>
            <div class="project-actions">
                <button class="cyber-btn">ANALYZE</button>
                <button class="cyber-btn">PREDICT</button>
            </div>
        </div>
    </div>

    <div class="terminal">
        <div class="terminal-line" style="animation-delay: 0s;">> SYSTEM INITIALIZED</div>
        <div class="terminal-line" style="animation-delay: 1s;">> LOADING PROJECTS...</div>
        <div class="terminal-line" style="animation-delay: 2s;">> CONNECTION ESTABLISHED</div>
        <div class="terminal-line" style="animation-delay: 3s;">> READY FOR INPUT_</div>
    </div>

    <script>
        // 添加随机故障效果
        setInterval(() => {
            const cards = document.querySelectorAll('.project-card');
            const randomCard = cards[Math.floor(Math.random() * cards.length)];
            randomCard.style.filter = 'hue-rotate(180deg)';
            setTimeout(() => {
                randomCard.style.filter = 'none';
            }, 200);
        }, 5000);

        // 按钮点击效果
        document.querySelectorAll('.cyber-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 100);
            });
        });
    </script>
</body>
</html>
