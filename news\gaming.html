<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Hub - 游戏化项目展示</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background: 
                radial-gradient(circle at 25% 25%, #1a0033 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, #003366 0%, transparent 50%),
                linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #00ffff;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                repeating-linear-gradient(
                    90deg,
                    transparent,
                    transparent 98px,
                    rgba(0, 255, 255, 0.03) 100px
                ),
                repeating-linear-gradient(
                    0deg,
                    transparent,
                    transparent 98px,
                    rgba(0, 255, 255, 0.03) 100px
                );
            pointer-events: none;
            z-index: 1;
        }

        .hud {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #00ffff;
            border-radius: 10px;
            padding: 15px 25px;
            backdrop-filter: blur(10px);
        }

        .hud-left {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .player-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .avatar {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #00ffff, #0080ff);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            border: 2px solid #00ffff;
        }

        .player-stats {
            font-family: 'Orbitron', monospace;
            font-size: 0.9rem;
        }

        .level {
            color: #ffff00;
            font-weight: 700;
        }

        .xp-bar {
            width: 150px;
            height: 8px;
            background: rgba(0, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }

        .xp-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ffff, #ffff00);
            width: 75%;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .hud-right {
            display: flex;
            gap: 20px;
        }

        .hud-button {
            background: transparent;
            border: 2px solid #00ffff;
            color: #00ffff;
            padding: 8px 16px;
            border-radius: 5px;
            font-family: 'Orbitron', monospace;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .hud-button:hover {
            background: #00ffff;
            color: #000;
            box-shadow: 0 0 20px #00ffff;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 120px 40px 60px;
            position: relative;
            z-index: 2;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
        }

        .header h1 {
            font-family: 'Orbitron', monospace;
            font-size: 4rem;
            font-weight: 900;
            color: #00ffff;
            text-shadow: 
                0 0 10px #00ffff,
                0 0 20px #00ffff,
                0 0 40px #00ffff;
            margin-bottom: 20px;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 10px #00ffff, 0 0 20px #00ffff, 0 0 40px #00ffff; }
            to { text-shadow: 0 0 20px #00ffff, 0 0 30px #00ffff, 0 0 60px #00ffff; }
        }

        .header p {
            font-size: 1.3rem;
            color: #80ffff;
            font-weight: 400;
        }

        .game-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }

        .game-card {
            background: linear-gradient(145deg, rgba(0, 51, 102, 0.3), rgba(26, 0, 51, 0.3));
            border: 2px solid #00ffff;
            border-radius: 15px;
            padding: 0;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .game-card::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #00ffff, #0080ff, #8000ff, #ff0080, #00ffff);
            background-size: 400% 400%;
            border-radius: 15px;
            z-index: -1;
            opacity: 0;
            animation: borderGlow 4s ease infinite;
        }

        .game-card:hover::before {
            opacity: 1;
        }

        @keyframes borderGlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .game-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 255, 255, 0.3);
        }

        .game-header {
            background: linear-gradient(135deg, #003366, #001a33);
            padding: 25px;
            border-bottom: 2px solid #00ffff;
            position: relative;
        }

        .game-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #00ffff, #0080ff);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin-bottom: 15px;
            border: 2px solid #00ffff;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        }

        .game-title {
            font-family: 'Orbitron', monospace;
            font-size: 1.5rem;
            font-weight: 700;
            color: #00ffff;
            margin-bottom: 8px;
        }

        .game-genre {
            font-size: 0.9rem;
            color: #80ffff;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .difficulty {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #ff0080;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .game-content {
            padding: 25px;
        }

        .game-description {
            font-size: 1rem;
            line-height: 1.6;
            color: #cccccc;
            margin-bottom: 20px;
        }

        .game-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 25px;
        }

        .stat-box {
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .stat-value {
            font-family: 'Orbitron', monospace;
            font-size: 1.2rem;
            font-weight: 700;
            color: #ffff00;
            display: block;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #80ffff;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .achievements {
            display: flex;
            gap: 10px;
            margin-bottom: 25px;
        }

        .achievement {
            width: 30px;
            height: 30px;
            background: linear-gradient(45deg, #ffff00, #ff8000);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            border: 2px solid #ffff00;
            box-shadow: 0 0 10px rgba(255, 255, 0, 0.5);
        }

        .game-actions {
            display: flex;
            gap: 15px;
        }

        .game-btn {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #00ffff;
            border-radius: 8px;
            background: transparent;
            color: #00ffff;
            font-family: 'Orbitron', monospace;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            position: relative;
            overflow: hidden;
        }

        .game-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .game-btn:hover::before {
            left: 100%;
        }

        .game-btn:hover {
            background: #00ffff;
            color: #000;
            box-shadow: 0 0 20px #00ffff;
            transform: translateY(-2px);
        }

        .btn-primary {
            border-color: #ffff00;
            color: #ffff00;
        }

        .btn-primary:hover {
            background: #ffff00;
            box-shadow: 0 0 20px #ffff00;
        }

        .power-ups {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            z-index: 100;
        }

        .power-up {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 2px solid #00ffff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            animation: float 3s ease-in-out infinite;
        }

        .power-up:nth-child(1) {
            background: linear-gradient(45deg, #ff0080, #8000ff);
            animation-delay: 0s;
        }

        .power-up:nth-child(2) {
            background: linear-gradient(45deg, #00ffff, #0080ff);
            animation-delay: 1s;
        }

        .power-up:nth-child(3) {
            background: linear-gradient(45deg, #ffff00, #ff8000);
            animation-delay: 2s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .power-up:hover {
            transform: scale(1.2);
            box-shadow: 0 0 20px currentColor;
        }

        @media (max-width: 768px) {
            .container {
                padding: 100px 20px 60px;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
            
            .game-grid {
                grid-template-columns: 1fr;
                gap: 25px;
            }
            
            .hud {
                flex-direction: column;
                gap: 15px;
            }
            
            .power-ups {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="hud">
        <div class="hud-left">
            <div class="player-info">
                <div class="avatar">🎮</div>
                <div class="player-stats">
                    <div>Player: <span class="level">Level 42</span></div>
                    <div class="xp-bar">
                        <div class="xp-fill"></div>
                    </div>
                </div>
            </div>
            <div>Score: <span style="color: #ffff00;">15,847</span></div>
        </div>
        <div class="hud-right">
            <button class="hud-button">INVENTORY</button>
            <button class="hud-button">SETTINGS</button>
            <button class="hud-button">QUIT</button>
        </div>
    </div>

    <div class="power-ups">
        <div class="power-up">⚡</div>
        <div class="power-up">🛡️</div>
        <div class="power-up">💎</div>
    </div>

    <div class="container">
        <header class="header">
            <h1>GAME HUB</h1>
            <p>// 进入游戏化的项目世界 //</p>
        </header>

        <div class="game-grid">
            <div class="game-card">
                <div class="game-header">
                    <div class="difficulty">EPIC</div>
                    <div class="game-icon">🏪</div>
                    <h3 class="game-title">QUEST MARKET</h3>
                    <p class="game-genre">RPG · Adventure</p>
                </div>
                <div class="game-content">
                    <p class="game-description">
                        踏上史诗级的购物冒险！每次购买都是一次任务，每个商品都有稀有度等级。
                        收集装备、升级角色、解锁成就，让购物变成一场激动人心的游戏！
                    </p>
                    <div class="game-stats">
                        <div class="stat-box">
                            <span class="stat-value">9.2K</span>
                            <span class="stat-label">Players</span>
                        </div>
                        <div class="stat-box">
                            <span class="stat-value">★★★★★</span>
                            <span class="stat-label">Rating</span>
                        </div>
                        <div class="stat-box">
                            <span class="stat-value">ONLINE</span>
                            <span class="stat-label">Status</span>
                        </div>
                    </div>
                    <div class="achievements">
                        <div class="achievement">🏆</div>
                        <div class="achievement">⭐</div>
                        <div class="achievement">💎</div>
                        <div class="achievement">🎯</div>
                    </div>
                    <div class="game-actions">
                        <button class="game-btn btn-primary">PLAY NOW</button>
                        <button class="game-btn">PREVIEW</button>
                    </div>
                </div>
            </div>

            <div class="game-card">
                <div class="game-header">
                    <div class="difficulty">HARD</div>
                    <div class="game-icon">🏢</div>
                    <h3 class="game-title">OFFICE TYCOON</h3>
                    <p class="game-genre">Strategy · Simulation</p>
                </div>
                <div class="game-content">
                    <p class="game-description">
                        建造你的商业帝国！管理资源、招募员工、扩展业务。
                        每个决策都影响公司发展，体验从初创到上市的完整商业旅程。
                    </p>
                    <div class="game-stats">
                        <div class="stat-box">
                            <span class="stat-value">680</span>
                            <span class="stat-label">Companies</span>
                        </div>
                        <div class="stat-box">
                            <span class="stat-value">96%</span>
                            <span class="stat-label">Success</span>
                        </div>
                        <div class="stat-box">
                            <span class="stat-value">ACTIVE</span>
                            <span class="stat-label">Economy</span>
                        </div>
                    </div>
                    <div class="achievements">
                        <div class="achievement">💼</div>
                        <div class="achievement">📈</div>
                        <div class="achievement">👑</div>
                        <div class="achievement">🌟</div>
                    </div>
                    <div class="game-actions">
                        <button class="game-btn btn-primary">START EMPIRE</button>
                        <button class="game-btn">TUTORIAL</button>
                    </div>
                </div>
            </div>

            <div class="game-card">
                <div class="game-header">
                    <div class="difficulty">NORMAL</div>
                    <div class="game-icon">🎓</div>
                    <h3 class="game-title">KNOWLEDGE QUEST</h3>
                    <p class="game-genre">Educational · Adventure</p>
                </div>
                <div class="game-content">
                    <p class="game-description">
                        在知识的世界中展开冒险！解答问题获得经验值，完成课程解锁新技能。
                        与其他学习者组队，在竞技场中展示你的智慧！
                    </p>
                    <div class="game-stats">
                        <div class="stat-box">
                            <span class="stat-value">32.1K</span>
                            <span class="stat-label">Students</span>
                        </div>
                        <div class="stat-box">
                            <span class="stat-value">94%</span>
                            <span class="stat-label">Completion</span>
                        </div>
                        <div class="stat-box">
                            <span class="stat-value">LIVE</span>
                            <span class="stat-label">Battles</span>
                        </div>
                    </div>
                    <div class="achievements">
                        <div class="achievement">📚</div>
                        <div class="achievement">🧠</div>
                        <div class="achievement">🏅</div>
                        <div class="achievement">⚡</div>
                    </div>
                    <div class="game-actions">
                        <button class="game-btn btn-primary">JOIN QUEST</button>
                        <button class="game-btn">LEADERBOARD</button>
                    </div>
                </div>
            </div>

            <div class="game-card">
                <div class="game-header">
                    <div class="difficulty">LEGENDARY</div>
                    <div class="game-icon">📊</div>
                    <h3 class="game-title">DATA DUNGEON</h3>
                    <p class="game-genre">Puzzle · Analytics</p>
                </div>
                <div class="game-content">
                    <p class="game-description">
                        深入数据地牢，解开隐藏的秘密！每个图表都是一个谜题，
                        每个数据点都可能是宝藏。使用分析技能击败数据怪兽！
                    </p>
                    <div class="game-stats">
                        <div class="stat-box">
                            <span class="stat-value">12.8M</span>
                            <span class="stat-label">Data Points</span>
                        </div>
                        <div class="stat-box">
                            <span class="stat-value">99.9%</span>
                            <span class="stat-label">Accuracy</span>
                        </div>
                        <div class="stat-box">
                            <span class="stat-value">REAL-TIME</span>
                            <span class="stat-label">Updates</span>
                        </div>
                    </div>
                    <div class="achievements">
                        <div class="achievement">🔍</div>
                        <div class="achievement">📈</div>
                        <div class="achievement">🗝️</div>
                        <div class="achievement">👑</div>
                    </div>
                    <div class="game-actions">
                        <button class="game-btn btn-primary">ENTER DUNGEON</button>
                        <button class="game-btn">GUILD</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 游戏音效模拟
        function playGameSound(type) {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                switch(type) {
                    case 'click':
                        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                        oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);
                        break;
                    case 'hover':
                        oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
                        break;
                    case 'achievement':
                        oscillator.frequency.setValueAtTime(523, audioContext.currentTime);
                        oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.1);
                        oscillator.frequency.setValueAtTime(784, audioContext.currentTime + 0.2);
                        break;
                }

                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.2);
            } catch(e) {
                // 静默处理音频错误
            }
        }

        // 按钮交互效果
        document.querySelectorAll('.game-btn, .hud-button').forEach(btn => {
            btn.addEventListener('mouseenter', () => playGameSound('hover'));
            btn.addEventListener('click', function() {
                playGameSound('click');
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 100);
            });
        });

        // 成就点击效果
        document.querySelectorAll('.achievement').forEach(achievement => {
            achievement.addEventListener('click', function() {
                playGameSound('achievement');
                this.style.animation = 'none';
                this.offsetHeight; // 触发重排
                this.style.animation = 'pulse 0.5s ease';
            });
        });

        // 经验值动画
        function updateXP() {
            const xpFill = document.querySelector('.xp-fill');
            const currentWidth = parseInt(xpFill.style.width) || 75;
            const newWidth = Math.min(currentWidth + Math.random() * 5, 100);
            xpFill.style.width = newWidth + '%';

            if (newWidth >= 100) {
                // 升级效果
                setTimeout(() => {
                    xpFill.style.width = '0%';
                    playGameSound('achievement');
                }, 500);
            }
        }

        // 卡片进入动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0) scale(1)';
                }
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.game-card').forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px) scale(0.9)';
                card.style.transition = `all 0.8s ease ${index * 0.2}s`;
                observer.observe(card);
            });

            // 定期更新经验值
            setInterval(updateXP, 5000);
        });

        // Power-up 点击效果
        document.querySelectorAll('.power-up').forEach(powerUp => {
            powerUp.addEventListener('click', function() {
                playGameSound('achievement');
                this.style.transform = 'scale(1.5)';
                this.style.opacity = '0';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                    this.style.opacity = '1';
                }, 300);
            });
        });

        // 随机事件系统
        setInterval(() => {
            const events = [
                '🎉 新成就解锁！',
                '⚡ 获得能量提升！',
                '💎 发现稀有物品！',
                '🏆 排名上升！'
            ];

            const randomEvent = events[Math.floor(Math.random() * events.length)];

            // 创建通知
            const notification = document.createElement('div');
            notification.style.position = 'fixed';
            notification.style.top = '100px';
            notification.style.right = '20px';
            notification.style.background = 'rgba(0, 255, 255, 0.9)';
            notification.style.color = '#000';
            notification.style.padding = '10px 20px';
            notification.style.borderRadius = '5px';
            notification.style.fontFamily = 'Orbitron, monospace';
            notification.style.fontSize = '0.9rem';
            notification.style.zIndex = '1001';
            notification.style.transform = 'translateX(100%)';
            notification.style.transition = 'transform 0.3s ease';
            notification.textContent = randomEvent;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);

            playGameSound('achievement');
        }, 15000);
    </script>
</body>
</html>
