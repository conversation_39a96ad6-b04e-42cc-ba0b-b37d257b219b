<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Material Projects - 材料设计项目展示</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');
        @import url('https://fonts.googleapis.com/icon?family=Material+Icons');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: #fafafa;
            color: #212121;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
        }

        .header {
            text-align: center;
            margin-bottom: 48px;
            padding: 48px 0;
        }

        .header h1 {
            font-size: 3.5rem;
            font-weight: 300;
            color: #1976d2;
            margin-bottom: 16px;
            letter-spacing: -0.5px;
        }

        .header p {
            font-size: 1.25rem;
            color: #757575;
            font-weight: 400;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 24px;
        }

        .material-card {
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 
                0 2px 4px rgba(0,0,0,0.1),
                0 8px 16px rgba(0,0,0,0.1);
            transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
            overflow: hidden;
            position: relative;
        }

        .material-card:hover {
            box-shadow: 
                0 4px 8px rgba(0,0,0,0.12),
                0 16px 24px rgba(0,0,0,0.14);
            transform: translateY(-4px);
        }

        .card-header {
            background: linear-gradient(135deg, #1976d2, #42a5f5);
            color: white;
            padding: 24px;
            position: relative;
            overflow: hidden;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
            background-size: 50px 50px;
        }

        .card-icon {
            width: 56px;
            height: 56px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            position: relative;
            z-index: 1;
        }

        .card-icon .material-icons {
            font-size: 28px;
            color: white;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 500;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .card-subtitle {
            font-size: 0.875rem;
            opacity: 0.8;
            position: relative;
            z-index: 1;
        }

        .card-content {
            padding: 24px;
        }

        .card-description {
            font-size: 1rem;
            color: #616161;
            line-height: 1.7;
            margin-bottom: 24px;
        }

        .metrics-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }

        .metric-item {
            text-align: center;
            padding: 16px;
            background: #f5f5f5;
            border-radius: 8px;
            transition: background-color 0.2s ease;
        }

        .metric-item:hover {
            background: #e3f2fd;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 500;
            color: #1976d2;
            display: block;
        }

        .metric-label {
            font-size: 0.75rem;
            color: #757575;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 500;
        }

        .chip-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 24px;
        }

        .chip {
            background: #e3f2fd;
            color: #1976d2;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 0.75rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .chip .material-icons {
            font-size: 16px;
        }

        .card-actions {
            padding: 8px 16px 16px;
            display: flex;
            gap: 8px;
        }

        .material-btn {
            padding: 10px 24px;
            border: none;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            font-family: inherit;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        .material-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.3s, height 0.3s;
        }

        .material-btn:active::before {
            width: 300px;
            height: 300px;
        }

        .btn-primary {
            background: #1976d2;
            color: white;
        }

        .btn-primary:hover {
            background: #1565c0;
            box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: #1976d2;
            border: 1px solid #1976d2;
        }

        .btn-secondary:hover {
            background: rgba(25, 118, 210, 0.04);
        }

        .fab {
            position: fixed;
            bottom: 24px;
            right: 24px;
            width: 56px;
            height: 56px;
            background: #ff4081;
            border: none;
            border-radius: 50%;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
            z-index: 100;
        }

        .fab:hover {
            background: #e91e63;
            box-shadow: 0 6px 12px rgba(0,0,0,0.4);
            transform: scale(1.1);
        }

        .fab .material-icons {
            color: white;
            font-size: 24px;
        }

        .app-bar {
            background: #1976d2;
            color: white;
            padding: 16px 24px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .app-bar h2 {
            font-size: 1.25rem;
            font-weight: 500;
        }

        .nav-links {
            display: flex;
            gap: 24px;
            margin-left: auto;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .main-content {
            margin-top: 80px;
        }

        .progress-indicator {
            position: fixed;
            top: 64px;
            left: 0;
            height: 4px;
            background: #ff4081;
            transition: width 0.3s ease;
            z-index: 999;
        }

        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
            
            .projects-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .card-content {
                padding: 16px;
            }
            
            .nav-links {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="progress-indicator" id="progressBar"></div>
    
    <header class="app-bar">
        <h2>Material Projects</h2>
        <nav class="nav-links">
            <a href="#" class="nav-link">首页</a>
            <a href="#" class="nav-link">项目</a>
            <a href="#" class="nav-link">关于</a>
            <a href="#" class="nav-link">联系</a>
        </nav>
    </header>

    <main class="main-content">
        <div class="container">
            <section class="header">
                <h1>Material Projects</h1>
                <p>遵循 Google Material Design 设计规范的项目展示</p>
            </section>

            <div class="projects-grid">
                <div class="material-card">
                    <div class="card-header">
                        <div class="card-icon">
                            <span class="material-icons">shopping_cart</span>
                        </div>
                        <h3 class="card-title">智能商城</h3>
                        <p class="card-subtitle">Material Design 电商平台</p>
                    </div>
                    <div class="card-content">
                        <p class="card-description">
                            严格遵循 Material Design 设计语言的现代化电商平台。
                            采用层次分明的卡片布局、流畅的动画效果和直观的交互设计，
                            为用户提供愉悦的购物体验。
                        </p>
                        <div class="chip-container">
                            <div class="chip">
                                <span class="material-icons">code</span>
                                React
                            </div>
                            <div class="chip">
                                <span class="material-icons">palette</span>
                                Material-UI
                            </div>
                            <div class="chip">
                                <span class="material-icons">storage</span>
                                Firebase
                            </div>
                        </div>
                        <div class="metrics-container">
                            <div class="metric-item">
                                <span class="metric-value">6.8K</span>
                                <span class="metric-label">用户</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">4.7</span>
                                <span class="metric-label">评分</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">98%</span>
                                <span class="metric-label">满意度</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="material-btn btn-primary">立即体验</button>
                        <button class="material-btn btn-secondary">了解更多</button>
                    </div>
                </div>

                <div class="material-card">
                    <div class="card-header" style="background: linear-gradient(135deg, #4caf50, #81c784);">
                        <div class="card-icon">
                            <span class="material-icons">work</span>
                        </div>
                        <h3 class="card-title">协作办公</h3>
                        <p class="card-subtitle">团队协作管理系统</p>
                    </div>
                    <div class="card-content">
                        <p class="card-description">
                            基于 Material Design 的企业级办公协作平台。
                            提供项目管理、团队沟通、文档协作等功能，
                            界面简洁明了，操作流畅自然。
                        </p>
                        <div class="chip-container">
                            <div class="chip">
                                <span class="material-icons">groups</span>
                                团队协作
                            </div>
                            <div class="chip">
                                <span class="material-icons">task</span>
                                项目管理
                            </div>
                            <div class="chip">
                                <span class="material-icons">chat</span>
                                实时通讯
                            </div>
                        </div>
                        <div class="metrics-container">
                            <div class="metric-item">
                                <span class="metric-value">320</span>
                                <span class="metric-label">企业</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">95%</span>
                                <span class="metric-label">效率提升</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">24/7</span>
                                <span class="metric-label">支持</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="material-btn btn-primary">开始使用</button>
                        <button class="material-btn btn-secondary">功能介绍</button>
                    </div>
                </div>

                <div class="material-card">
                    <div class="card-header" style="background: linear-gradient(135deg, #ff9800, #ffb74d);">
                        <div class="card-icon">
                            <span class="material-icons">school</span>
                        </div>
                        <h3 class="card-title">在线学堂</h3>
                        <p class="card-subtitle">现代化教育平台</p>
                    </div>
                    <div class="card-content">
                        <p class="card-description">
                            采用 Material Design 设计的在线教育平台。
                            提供课程学习、作业提交、成绩查询等功能，
                            界面友好，学习体验流畅。
                        </p>
                        <div class="chip-container">
                            <div class="chip">
                                <span class="material-icons">video_library</span>
                                视频课程
                            </div>
                            <div class="chip">
                                <span class="material-icons">quiz</span>
                                在线测试
                            </div>
                            <div class="chip">
                                <span class="material-icons">certificate</span>
                                认证证书
                            </div>
                        </div>
                        <div class="metrics-container">
                            <div class="metric-item">
                                <span class="metric-value">18.5K</span>
                                <span class="metric-label">学生</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">92%</span>
                                <span class="metric-label">完成率</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">4.8</span>
                                <span class="metric-label">好评</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="material-btn btn-primary">开始学习</button>
                        <button class="material-btn btn-secondary">课程目录</button>
                    </div>
                </div>

                <div class="material-card">
                    <div class="card-header" style="background: linear-gradient(135deg, #9c27b0, #ba68c8);">
                        <div class="card-icon">
                            <span class="material-icons">analytics</span>
                        </div>
                        <h3 class="card-title">数据分析</h3>
                        <p class="card-subtitle">智能数据洞察平台</p>
                    </div>
                    <div class="card-content">
                        <p class="card-description">
                            Material Design 风格的数据分析平台。
                            提供数据可视化、报表生成、趋势分析等功能，
                            让复杂的数据变得简单易懂。
                        </p>
                        <div class="chip-container">
                            <div class="chip">
                                <span class="material-icons">bar_chart</span>
                                数据可视化
                            </div>
                            <div class="chip">
                                <span class="material-icons">trending_up</span>
                                趋势分析
                            </div>
                            <div class="chip">
                                <span class="material-icons">report</span>
                                智能报表
                            </div>
                        </div>
                        <div class="metrics-container">
                            <div class="metric-item">
                                <span class="metric-value">4.2M</span>
                                <span class="metric-label">数据点</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">99.7%</span>
                                <span class="metric-label">准确率</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">实时</span>
                                <span class="metric-label">更新</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="material-btn btn-primary">查看数据</button>
                        <button class="material-btn btn-secondary">生成报表</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <button class="fab">
        <span class="material-icons">add</span>
    </button>

    <script>
        // 滚动进度条
        window.addEventListener('scroll', () => {
            const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
            document.getElementById('progressBar').style.width = scrolled + '%';
        });

        // 卡片进入动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.material-card').forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = `all 0.6s cubic-bezier(0.4, 0.0, 0.2, 1) ${index * 0.1}s`;
                observer.observe(card);
            });
        });

        // 按钮波纹效果
        document.querySelectorAll('.material-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.style.width = '0';
                ripple.style.height = '0';
                ripple.style.background = 'rgba(255, 255, 255, 0.6)';
                ripple.style.borderRadius = '50%';
                ripple.style.transform = 'translate(-50%, -50%)';
                ripple.style.animation = 'ripple 0.6s linear';
                
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });

        // 添加波纹动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    width: 200px;
                    height: 200px;
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
