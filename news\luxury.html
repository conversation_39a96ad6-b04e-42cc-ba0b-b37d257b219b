<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prestige Portfolio - 尊贵项目展示</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;700;900&family=Montserrat:wght@300;400;500;600&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Montserrat', sans-serif;
            background: 
                radial-gradient(circle at 20% 20%, rgba(218, 165, 32, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(139, 69, 19, 0.1) 0%, transparent 50%),
                linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
            color: #f5f5f5;
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                repeating-linear-gradient(
                    0deg,
                    transparent,
                    transparent 2px,
                    rgba(218, 165, 32, 0.02) 2px,
                    rgba(218, 165, 32, 0.02) 4px
                );
            pointer-events: none;
            z-index: 1;
        }

        .luxury-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(218, 165, 32, 0.3);
            z-index: 1000;
            padding: 20px 0;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 40px;
        }

        .logo {
            font-family: 'Playfair Display', serif;
            font-size: 1.8rem;
            font-weight: 700;
            color: #daa520;
            letter-spacing: 2px;
        }

        .nav-menu {
            display: flex;
            gap: 40px;
        }

        .nav-item {
            color: #f5f5f5;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 400;
            letter-spacing: 1px;
            text-transform: uppercase;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-item::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #daa520, #b8860b);
            transition: width 0.3s ease;
        }

        .nav-item:hover {
            color: #daa520;
        }

        .nav-item:hover::after {
            width: 100%;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 120px 40px 80px;
            position: relative;
            z-index: 2;
        }

        .hero-section {
            text-align: center;
            margin-bottom: 100px;
            position: relative;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: -50px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 2px;
            background: linear-gradient(90deg, transparent, #daa520, transparent);
        }

        .hero-title {
            font-family: 'Playfair Display', serif;
            font-size: 4.5rem;
            font-weight: 900;
            color: #daa520;
            margin-bottom: 30px;
            letter-spacing: 3px;
            text-shadow: 0 0 30px rgba(218, 165, 32, 0.3);
        }

        .hero-subtitle {
            font-size: 1.4rem;
            color: #cccccc;
            font-weight: 300;
            letter-spacing: 2px;
            margin-bottom: 20px;
        }

        .hero-description {
            font-size: 1.1rem;
            color: #999999;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.8;
        }

        .portfolio-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
            gap: 50px;
        }

        .luxury-card {
            background: linear-gradient(145deg, rgba(45, 45, 45, 0.8), rgba(26, 26, 26, 0.8));
            border: 1px solid rgba(218, 165, 32, 0.2);
            border-radius: 20px;
            overflow: hidden;
            transition: all 0.5s cubic-bezier(0.23, 1, 0.320, 1);
            position: relative;
            backdrop-filter: blur(20px);
        }

        .luxury-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(218, 165, 32, 0.1), transparent, rgba(139, 69, 19, 0.1));
            opacity: 0;
            transition: opacity 0.5s ease;
            pointer-events: none;
        }

        .luxury-card:hover::before {
            opacity: 1;
        }

        .luxury-card:hover {
            transform: translateY(-20px);
            border-color: rgba(218, 165, 32, 0.5);
            box-shadow: 
                0 30px 60px rgba(0, 0, 0, 0.4),
                0 0 50px rgba(218, 165, 32, 0.1);
        }

        .card-header {
            padding: 40px;
            border-bottom: 1px solid rgba(218, 165, 32, 0.2);
            position: relative;
        }

        .premium-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(45deg, #daa520, #b8860b);
            color: #000;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .card-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, rgba(218, 165, 32, 0.2), rgba(139, 69, 19, 0.2));
            border: 2px solid rgba(218, 165, 32, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            margin-bottom: 25px;
            transition: all 0.3s ease;
        }

        .luxury-card:hover .card-icon {
            background: linear-gradient(45deg, rgba(218, 165, 32, 0.4), rgba(139, 69, 19, 0.4));
            border-color: rgba(218, 165, 32, 0.6);
            transform: scale(1.1);
        }

        .card-title {
            font-family: 'Playfair Display', serif;
            font-size: 2rem;
            font-weight: 700;
            color: #daa520;
            margin-bottom: 15px;
            letter-spacing: 1px;
        }

        .card-category {
            font-size: 0.9rem;
            color: #999999;
            text-transform: uppercase;
            letter-spacing: 2px;
            font-weight: 500;
        }

        .card-content {
            padding: 40px;
        }

        .card-description {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #cccccc;
            margin-bottom: 35px;
        }

        .luxury-metrics {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 25px;
            margin-bottom: 35px;
        }

        .metric-box {
            background: rgba(218, 165, 32, 0.05);
            border: 1px solid rgba(218, 165, 32, 0.2);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .metric-box:hover {
            background: rgba(218, 165, 32, 0.1);
            border-color: rgba(218, 165, 32, 0.4);
        }

        .metric-value {
            font-family: 'Playfair Display', serif;
            font-size: 2rem;
            font-weight: 700;
            color: #daa520;
            display: block;
            margin-bottom: 8px;
        }

        .metric-label {
            font-size: 0.8rem;
            color: #999999;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 500;
        }

        .premium-features {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 35px;
        }

        .feature-tag {
            background: rgba(218, 165, 32, 0.1);
            border: 1px solid rgba(218, 165, 32, 0.3);
            color: #daa520;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.8rem;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .luxury-actions {
            display: flex;
            gap: 20px;
        }

        .luxury-btn {
            flex: 1;
            padding: 18px 30px;
            border: 2px solid #daa520;
            border-radius: 8px;
            background: transparent;
            color: #daa520;
            font-family: 'Montserrat', sans-serif;
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .luxury-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(218, 165, 32, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .luxury-btn:hover::before {
            left: 100%;
        }

        .luxury-btn:hover {
            background: #daa520;
            color: #000;
            box-shadow: 0 10px 30px rgba(218, 165, 32, 0.3);
            transform: translateY(-3px);
        }

        .btn-primary {
            background: linear-gradient(45deg, #daa520, #b8860b);
            color: #000;
            border-color: #daa520;
        }

        .btn-primary:hover {
            background: linear-gradient(45deg, #b8860b, #daa520);
            box-shadow: 0 15px 40px rgba(218, 165, 32, 0.4);
        }

        .floating-contact {
            position: fixed;
            bottom: 40px;
            right: 40px;
            width: 70px;
            height: 70px;
            background: linear-gradient(45deg, #daa520, #b8860b);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: #000;
            text-decoration: none;
            box-shadow: 0 10px 30px rgba(218, 165, 32, 0.4);
            transition: all 0.3s ease;
            z-index: 100;
        }

        .floating-contact:hover {
            transform: scale(1.1);
            box-shadow: 0 15px 40px rgba(218, 165, 32, 0.6);
        }

        @media (max-width: 768px) {
            .container {
                padding: 100px 20px 60px;
            }
            
            .hero-title {
                font-size: 3rem;
            }
            
            .portfolio-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .luxury-card {
                margin: 0 10px;
            }
            
            .nav-menu {
                display: none;
            }
            
            .header-content {
                padding: 0 20px;
            }
        }
    </style>
</head>
<body>
    <header class="luxury-header">
        <div class="header-content">
            <div class="logo">PRESTIGE</div>
            <nav class="nav-menu">
                <a href="#" class="nav-item">Portfolio</a>
                <a href="#" class="nav-item">Services</a>
                <a href="#" class="nav-item">About</a>
                <a href="#" class="nav-item">Contact</a>
            </nav>
        </div>
    </header>

    <a href="#" class="floating-contact">💎</a>

    <div class="container">
        <section class="hero-section">
            <h1 class="hero-title">PRESTIGE PORTFOLIO</h1>
            <p class="hero-subtitle">— 卓越品质 · 尊贵体验 —</p>
            <p class="hero-description">
                为追求极致品质的客户打造的专属项目展示平台。
                每一个项目都代表着我们对完美的不懈追求和对细节的极致关注。
            </p>
        </section>

        <div class="portfolio-grid">
            <div class="luxury-card">
                <div class="card-header">
                    <div class="premium-badge">PLATINUM</div>
                    <div class="card-icon">🏛️</div>
                    <h3 class="card-title">Royal Commerce</h3>
                    <p class="card-category">Luxury E-Commerce</p>
                </div>
                <div class="card-content">
                    <p class="card-description">
                        为高端品牌量身定制的奢华电商平台。采用顶级设计理念，
                        融合现代科技与传统工艺美学，为尊贵客户提供无与伦比的购物体验。
                        每一个细节都体现着品牌的尊贵与优雅。
                    </p>
                    <div class="luxury-metrics">
                        <div class="metric-box">
                            <span class="metric-value">$2.8M</span>
                            <span class="metric-label">Monthly Revenue</span>
                        </div>
                        <div class="metric-box">
                            <span class="metric-value">99.8%</span>
                            <span class="metric-label">Client Satisfaction</span>
                        </div>
                    </div>
                    <div class="premium-features">
                        <div class="feature-tag">Private Label</div>
                        <div class="feature-tag">Concierge Service</div>
                        <div class="feature-tag">VIP Access</div>
                        <div class="feature-tag">Premium Support</div>
                    </div>
                    <div class="luxury-actions">
                        <button class="luxury-btn btn-primary">Experience</button>
                        <button class="luxury-btn">Portfolio</button>
                    </div>
                </div>
            </div>

            <div class="luxury-card">
                <div class="card-header">
                    <div class="premium-badge">DIAMOND</div>
                    <div class="card-icon">🏢</div>
                    <h3 class="card-title">Executive Suite</h3>
                    <p class="card-category">Enterprise Solutions</p>
                </div>
                <div class="card-content">
                    <p class="card-description">
                        专为C级高管打造的企业管理系统。集成先进的AI技术与人性化设计，
                        提供战略级决策支持。界面优雅简洁，功能强大全面，
                        是现代企业领导者的得力助手。
                    </p>
                    <div class="luxury-metrics">
                        <div class="metric-box">
                            <span class="metric-value">500+</span>
                            <span class="metric-label">Fortune Companies</span>
                        </div>
                        <div class="metric-box">
                            <span class="metric-value">40%</span>
                            <span class="metric-label">Efficiency Boost</span>
                        </div>
                    </div>
                    <div class="premium-features">
                        <div class="feature-tag">AI Analytics</div>
                        <div class="feature-tag">Real-time Insights</div>
                        <div class="feature-tag">Executive Dashboard</div>
                        <div class="feature-tag">24/7 Concierge</div>
                    </div>
                    <div class="luxury-actions">
                        <button class="luxury-btn btn-primary">Schedule Demo</button>
                        <button class="luxury-btn">Learn More</button>
                    </div>
                </div>
            </div>

            <div class="luxury-card">
                <div class="card-header">
                    <div class="premium-badge">GOLD</div>
                    <div class="card-icon">🎓</div>
                    <h3 class="card-title">Elite Academy</h3>
                    <p class="card-category">Premium Education</p>
                </div>
                <div class="card-content">
                    <p class="card-description">
                        面向精英人士的高端教育平台。汇聚全球顶尖师资，
                        提供个性化学习方案。采用沉浸式学习体验设计，
                        让知识获取成为一种享受，助力学员达到人生新高度。
                    </p>
                    <div class="luxury-metrics">
                        <div class="metric-box">
                            <span class="metric-value">98%</span>
                            <span class="metric-label">Success Rate</span>
                        </div>
                        <div class="metric-box">
                            <span class="metric-value">$180K</span>
                            <span class="metric-label">Avg Salary Increase</span>
                        </div>
                    </div>
                    <div class="premium-features">
                        <div class="feature-tag">1-on-1 Mentoring</div>
                        <div class="feature-tag">Global Network</div>
                        <div class="feature-tag">Lifetime Access</div>
                        <div class="feature-tag">Career Coaching</div>
                    </div>
                    <div class="luxury-actions">
                        <button class="luxury-btn btn-primary">Apply Now</button>
                        <button class="luxury-btn">Curriculum</button>
                    </div>
                </div>
            </div>

            <div class="luxury-card">
                <div class="card-header">
                    <div class="premium-badge">EXCLUSIVE</div>
                    <div class="card-icon">📊</div>
                    <h3 class="card-title">Intelligence Hub</h3>
                    <p class="card-category">Strategic Analytics</p>
                </div>
                <div class="card-content">
                    <p class="card-description">
                        为战略决策者量身打造的智能分析平台。运用前沿的大数据技术，
                        提供深度市场洞察和预测分析。优雅的可视化界面，
                        让复杂数据变得清晰易懂，助力企业制定明智决策。
                    </p>
                    <div class="luxury-metrics">
                        <div class="metric-box">
                            <span class="metric-value">TB+</span>
                            <span class="metric-label">Data Processed</span>
                        </div>
                        <div class="metric-box">
                            <span class="metric-value">95%</span>
                            <span class="metric-label">Prediction Accuracy</span>
                        </div>
                    </div>
                    <div class="premium-features">
                        <div class="feature-tag">Predictive AI</div>
                        <div class="feature-tag">Custom Reports</div>
                        <div class="feature-tag">Real-time Alerts</div>
                        <div class="feature-tag">Expert Consultation</div>
                    </div>
                    <div class="luxury-actions">
                        <button class="luxury-btn btn-primary">Request Access</button>
                        <button class="luxury-btn">Case Studies</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 奢华粒子效果
        function createLuxuryParticles() {
            const particles = ['✨', '💎', '⭐', '🌟'];
            
            for (let i = 0; i < 15; i++) {
                const particle = document.createElement('div');
                particle.style.position = 'fixed';
                particle.style.left = Math.random() * 100 + 'vw';
                particle.style.top = Math.random() * 100 + 'vh';
                particle.style.fontSize = Math.random() * 15 + 8 + 'px';
                particle.style.color = '#daa520';
                particle.style.pointerEvents = 'none';
                particle.style.zIndex = '1';
                particle.style.opacity = '0.3';
                particle.textContent = particles[Math.floor(Math.random() * particles.length)];
                particle.style.animation = `luxuryFloat ${Math.random() * 15 + 10}s ease-in-out infinite`;
                particle.style.animationDelay = Math.random() * 5 + 's';
                
                document.body.appendChild(particle);
                
                setTimeout(() => {
                    particle.remove();
                }, 20000);
            }
        }

        // 添加奢华浮动动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes luxuryFloat {
                0%, 100% { 
                    transform: translateY(0px) rotate(0deg); 
                    opacity: 0.3; 
                }
                25% { 
                    transform: translateY(-20px) rotate(90deg); 
                    opacity: 0.6; 
                }
                50% { 
                    transform: translateY(-10px) rotate(180deg); 
                    opacity: 0.8; 
                }
                75% { 
                    transform: translateY(-30px) rotate(270deg); 
                    opacity: 0.4; 
                }
            }
        `;
        document.head.appendChild(style);

        // 卡片视差效果
        document.querySelectorAll('.luxury-card').forEach(card => {
            card.addEventListener('mousemove', (e) => {
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                
                const rotateX = (y - centerY) / 20;
                const rotateY = (centerX - x) / 20;
                
                card.style.transform = `translateY(-20px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) rotateX(0) rotateY(0)';
            });
        });

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            createLuxuryParticles();
            
            const cards = document.querySelectorAll('.luxury-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(80px) scale(0.9)';
                card.style.transition = `all 1s cubic-bezier(0.23, 1, 0.320, 1) ${index * 0.3}s`;
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0) scale(1)';
                }, index * 300);
            });
            
            // 定期创建新粒子
            setInterval(createLuxuryParticles, 15000);
        });

        // 按钮奢华效果
        document.querySelectorAll('.luxury-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 创建金色波纹效果
                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.borderRadius = '50%';
                ripple.style.background = 'rgba(218, 165, 32, 0.6)';
                ripple.style.transform = 'scale(0)';
                ripple.style.animation = 'luxuryRipple 0.6s linear';
                ripple.style.left = '50%';
                ripple.style.top = '50%';
                ripple.style.width = '20px';
                ripple.style.height = '20px';
                ripple.style.marginLeft = '-10px';
                ripple.style.marginTop = '-10px';
                
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });

        // 添加波纹动画
        const rippleStyle = document.createElement('style');
        rippleStyle.textContent = `
            @keyframes luxuryRipple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(rippleStyle);

        // 滚动视差效果
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.hero-section');
            const speed = scrolled * 0.5;
            
            if (parallax) {
                parallax.style.transform = `translateY(${speed}px)`;
            }
        });
    </script>
</body>
</html>
