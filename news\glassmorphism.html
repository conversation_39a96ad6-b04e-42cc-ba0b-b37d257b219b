<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Glass Projects - 玻璃拟态项目展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            color: #fff;
            overflow-x: hidden;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
            background-size: 100px 100px;
            animation: float 20s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 80px 40px;
        }

        .header {
            text-align: center;
            margin-bottom: 80px;
        }

        .header h1 {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.9);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
        }

        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 40px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .glass-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        }

        .glass-card:hover {
            transform: translateY(-10px);
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .project-icon {
            width: 70px;
            height: 70px;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin-bottom: 25px;
        }

        .project-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: rgba(255, 255, 255, 0.95);
        }

        .project-desc {
            font-size: 1rem;
            line-height: 1.7;
            margin-bottom: 30px;
            color: rgba(255, 255, 255, 0.8);
        }

        .project-metrics {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }

        .metric-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px;
            text-align: center;
        }

        .metric-value {
            font-size: 1.4rem;
            font-weight: 700;
            color: rgba(255, 255, 255, 0.95);
            display: block;
        }

        .metric-label {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.7);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .project-actions {
            display: flex;
            gap: 15px;
        }

        .glass-btn {
            flex: 1;
            padding: 15px 25px;
            border: none;
            border-radius: 12px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            position: relative;
            overflow: hidden;
        }

        .glass-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .glass-btn:hover::before {
            left: 100%;
        }

        .glass-btn-primary {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: rgba(255, 255, 255, 0.95);
        }

        .glass-btn-primary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .glass-btn-secondary {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.8);
        }

        .glass-btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.95);
        }

        .floating-nav {
            position: fixed;
            top: 40px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 50px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px 30px;
            display: flex;
            gap: 30px;
            z-index: 100;
        }

        .nav-item {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 8px 16px;
            border-radius: 20px;
        }

        .nav-item:hover {
            color: rgba(255, 255, 255, 1);
            background: rgba(255, 255, 255, 0.1);
        }

        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: particleFloat 15s infinite linear;
        }

        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 40px 20px;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
            
            .projects-grid {
                grid-template-columns: 1fr;
                gap: 25px;
            }
            
            .glass-card {
                padding: 30px;
            }
            
            .floating-nav {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="particles" id="particles"></div>
    
    <nav class="floating-nav">
        <a href="#" class="nav-item">首页</a>
        <a href="#" class="nav-item">关于</a>
        <a href="#" class="nav-item">服务</a>
        <a href="#" class="nav-item">联系</a>
    </nav>

    <div class="container">
        <header class="header">
            <h1>Glass Projects</h1>
            <p>透明如水晶的项目展示空间</p>
        </header>

        <div class="projects-grid">
            <div class="glass-card">
                <div class="project-icon">🌟</div>
                <h3 class="project-title">水晶商城</h3>
                <p class="project-desc">如水晶般透明纯净的购物体验，每个界面都经过精心打磨，让用户感受到玻璃般的质感和流畅度。</p>
                <div class="project-metrics">
                    <div class="metric-item">
                        <span class="metric-value">4.2K</span>
                        <span class="metric-label">活跃用户</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-value">98.5%</span>
                        <span class="metric-label">满意度</span>
                    </div>
                </div>
                <div class="project-actions">
                    <button class="glass-btn glass-btn-primary">立即体验</button>
                    <button class="glass-btn glass-btn-secondary">了解更多</button>
                </div>
            </div>

            <div class="glass-card">
                <div class="project-icon">💎</div>
                <h3 class="project-title">钻石办公</h3>
                <p class="project-desc">璀璨如钻石的办公系统，透明的界面设计让工作流程一目了然，提升团队协作效率。</p>
                <div class="project-metrics">
                    <div class="metric-item">
                        <span class="metric-value">230</span>
                        <span class="metric-label">企业用户</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-value">95%</span>
                        <span class="metric-label">效率提升</span>
                    </div>
                </div>
                <div class="project-actions">
                    <button class="glass-btn glass-btn-primary">开始使用</button>
                    <button class="glass-btn glass-btn-secondary">功能介绍</button>
                </div>
            </div>

            <div class="glass-card">
                <div class="project-icon">🔮</div>
                <h3 class="project-title">魔法学院</h3>
                <p class="project-desc">神秘而透明的在线教育平台，如魔法水晶球般展现知识的奥秘，让学习变得更加有趣。</p>
                <div class="project-metrics">
                    <div class="metric-item">
                        <span class="metric-value">12.8K</span>
                        <span class="metric-label">学习者</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-value">97%</span>
                        <span class="metric-label">完成率</span>
                    </div>
                </div>
                <div class="project-actions">
                    <button class="glass-btn glass-btn-primary">开始学习</button>
                    <button class="glass-btn glass-btn-secondary">课程目录</button>
                </div>
            </div>

            <div class="glass-card">
                <div class="project-icon">🌊</div>
                <h3 class="project-title">数据海洋</h3>
                <p class="project-desc">如海洋般深邃透明的数据分析平台，让复杂的数据如水流般清晰可见，洞察商业趋势。</p>
                <div class="project-metrics">
                    <div class="metric-item">
                        <span class="metric-value">2.1M</span>
                        <span class="metric-label">数据点</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-value">99.9%</span>
                        <span class="metric-label">准确率</span>
                    </div>
                </div>
                <div class="project-actions">
                    <button class="glass-btn glass-btn-primary">探索数据</button>
                    <button class="glass-btn glass-btn-secondary">分析报告</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 创建浮动粒子
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 20;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 15 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 卡片进入动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
            
            document.querySelectorAll('.glass-card').forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = `all 0.6s ease ${index * 0.1}s`;
                observer.observe(card);
            });
        });

        // 按钮点击效果
        document.querySelectorAll('.glass-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
    </script>
</body>
</html>
