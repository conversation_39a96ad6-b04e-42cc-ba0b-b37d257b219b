<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示站点导航 - 高度差</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
            position: relative;
        }

        @keyframes gradientShift {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
            animation: floatingBubbles 20s ease-in-out infinite;
        }

        @keyframes floatingBubbles {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 1;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 0.8;
            }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            transform: perspective(1000px) rotateX(10deg);
        }

        .header h1 {
            font-size: 3.5rem;
            color: white;
            text-shadow: 0 10px 20px rgba(0,0,0,0.3);
            margin-bottom: 10px;
            background: linear-gradient(45deg, #fff, #e0e0e0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .demo-card {
            background: rgba(255,255,255,0.12);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            position: relative;
            transform: perspective(1000px) rotateX(5deg) rotateY(-2deg);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 1px solid rgba(255,255,255,0.25);
            box-shadow:
                0 20px 40px rgba(0,0,0,0.15),
                0 0 0 1px rgba(255,255,255,0.15),
                inset 0 1px 0 rgba(255,255,255,0.25);
            cursor: pointer;
            overflow: hidden;
        }

        .demo-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .demo-card:hover::before {
            left: 100%;
        }

        .demo-card:hover {
            transform: perspective(1000px) rotateX(0deg) rotateY(0deg) translateY(-15px) scale(1.03);
            box-shadow:
                0 35px 70px rgba(0,0,0,0.25),
                0 0 0 1px rgba(255,255,255,0.35),
                inset 0 1px 0 rgba(255,255,255,0.35);
            background: rgba(255,255,255,0.18);
        }

        .demo-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            box-shadow: 0 10px 20px rgba(238, 90, 36, 0.3);
            transform: translateZ(20px);
        }

        .demo-card:nth-child(2n) .demo-icon {
            background: linear-gradient(135deg, #4834d4, #686de0);
            box-shadow: 0 10px 20px rgba(72, 52, 212, 0.3);
        }

        .demo-card:nth-child(3n) .demo-icon {
            background: linear-gradient(135deg, #00d2d3, #54a0ff);
            box-shadow: 0 10px 20px rgba(0, 210, 211, 0.3);
        }

        .demo-card:nth-child(4n) .demo-icon {
            background: linear-gradient(135deg, #5f27cd, #a55eea);
            box-shadow: 0 10px 20px rgba(95, 39, 205, 0.3);
        }

        .demo-title {
            font-size: 1.5rem;
            color: white;
            margin-bottom: 10px;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .demo-description {
            color: rgba(255,255,255,0.8);
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .demo-url {
            color: rgba(255,255,255,0.6);
            font-size: 0.85rem;
            font-family: 'Courier New', monospace;
            background: rgba(0,0,0,0.2);
            padding: 8px 12px;
            border-radius: 8px;
            display: inline-block;
            margin-bottom: 15px;
        }

        .visit-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(238, 90, 36, 0.4);
            text-decoration: none;
            display: inline-block;
        }

        .visit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(238, 90, 36, 0.6);
        }

        .nav-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255,255,255,0.12);
            backdrop-filter: blur(15px);
            border-bottom: 1px solid rgba(255,255,255,0.25);
            padding: 15px 0;
            z-index: 1000;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .logo-img {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .nav-links {
            display: flex;
            justify-content: center;
            gap: 30px;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 20px;
            border-radius: 20px;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .demo-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }

        .info-section {
            background: rgba(0,0,0,0.25);
            padding: 15px;
            border-radius: 12px;
            border: 1px solid rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
        }

        .info-title {
            color: rgba(255,255,255,0.9);
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .info-content {
            color: rgba(255,255,255,0.7);
            font-size: 0.85rem;
            line-height: 1.4;
        }

        .qr-code {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 8px;
            margin: 10px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            color: #666;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }

        .login-info {
            font-family: 'Courier New', monospace;
        }

        .demo-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 8px 16px;
            border-radius: 20px;
            border: none;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            box-shadow: 0 3px 10px rgba(238, 90, 36, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #4834d4, #686de0);
            color: white;
            box-shadow: 0 3px 10px rgba(72, 52, 212, 0.4);
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .btn-changelog {
            background: linear-gradient(135deg, #00d2d3, #54a0ff);
            color: white;
            box-shadow: 0 3px 10px rgba(0, 210, 211, 0.4);
        }



        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }

            .demo-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .demo-card {
                transform: none;
            }

            .demo-card:hover {
                transform: translateY(-5px) scale(1.01);
            }

            .nav-container {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                gap: 15px;
                flex-wrap: wrap;
                justify-content: center;
            }

            .demo-info {
                grid-template-columns: 1fr;
            }

            .demo-actions {
                flex-direction: column;
            }

            .container {
                padding-top: 120px;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="nav-header">
        <div class="nav-container">
            <a href="#" class="nav-logo">
                <div class="logo-img">
                    <img src="./images/logo.png" alt="logo" style="width: 60px;border-radius: 10px;"></img>
                </div>
                <span style="margin-left: 10px;">高度差网络</span>
            </a>
            <div class="nav-links">
                <a href="https://www.gaodux.com" class="nav-link" target="_blank">官网</a>
                <a href="https://bbs.gaodux.com" class="nav-link" target="_blank">论坛</a>
                <a href="https://bbs.gaodux.com/cate/15/seq/0" class="nav-link">☕ 聊聊合作</a>
            </div>
        </div>
    </div>

    <div class="container" style="padding-top: 100px;">
        <div class="header">
            <h1>系统演示站点</h1>
            <p>这些是我们团队精心打造的项目，每一个都有自己的故事</p>
        </div>

        <div class="demo-grid" id="demoGrid">
            <!-- 示例演示站点 -->
            <div class="demo-card">
                <div class="demo-icon">🌏</div>
                <h3 class="demo-title">跨境电商商城</h3>
                <p class="demo-description">智能零售+跨境电商商城系统，支持多种跨境贸易模式，提供一站式跨境解决方案，助力企业高效布局全球市场，打造私域流量增长新引擎。</p>

                <div class="demo-info">
                    <div class="info-section">
                        <div class="info-title">后台登陆信息</div>
                        <div class="info-content login-info">
                            <br/>
                            <p>账号: admin</p> 
                            <p>密码: admin321</p>
                        </div>
                    </div>
                    <div class="info-section">
                        <div class="info-title">小程序二维码</div>
                        <div>
                            <img src="./images/mini-program.png" alt="小程序二维码" style="width: 120px; border-radius: 15px;">
                        </div>
                    </div>
                </div>

                <div class="demo-actions">
                    <a href="https://www.gaodux.com/shop/" class="action-btn btn-primary" target="_blank">PC端演示</a>
                    <a href="https://www.gaodux.com/store" class="action-btn btn-secondary" target="_blank">后台管理</a>
                    <a href="#" class="action-btn btn-changelog" onclick="showStory('shop')">系统特色</a>
                </div>
            </div>

            <div class="demo-card">
                <div class="demo-icon">✈</div>
                <h3 class="demo-title">出口报关系统</h3>
                <p class="demo-description">跨境电商智能报关系统，支持多模式通关+全单证数字化申报，提供本地加签、批量导入、回执上报跟踪一站式解决方案，助力企业高效合规出海。</p>

                <div class="demo-info">
                    <div class="info-section">
                        <div class="info-title">体验账号</div>
                        <div class="info-content login-info">
                            账号: admin<br>
                            密码: admin321
                        </div>
                    </div>
                    <div class="info-section">
                        <div class="info-title">⚡ 核心功能</div>
                        <div class="info-content">
                            全流程数字化报关<br>
                            智能批量申报<br>
                            符合海关数据加密及申报规范
                        </div>
                    </div>
                </div>

                <div class="demo-actions">
                    <a href="https://9610.gaodux.com" class="action-btn btn-primary" target="_blank">查看后台</a>
                    <a href="https://bbs.gaodux.com/cate/11/seq/0" class="action-btn btn-changelog" target="_blank">更新日志</a>
                </div>
            </div>

            <!-- <div class="demo-card">
                <div class="demo-icon">⚓</div>
                <h3 class="demo-title">速通关-进口报关系统</h3>
                <p class="demo-description">疫情期间紧急上线的在线课堂，帮助一所中学的2000多名学生在家上课。老师们说比线下课堂互动还要好。</p>

                <div class="demo-info">
                    <div class="info-section">
                        <div class="info-title">🎭 角色体验</div>
                        <div class="info-content login-info">
                            王老师: wang_teacher<br>
                            密码: teach2024<br>
                            小明同学: xiaoming<br>
                            密码: study123
                        </div>
                    </div>
                    <div class="info-section">
                        <div class="info-title">� 使用数据</div>
                        <div class="info-content">
                            在线学生: 2,156人<br>
                            课程完成率: 94.2%<br>
                            家长满意度: 96%
                        </div>
                    </div>
                </div>

                <div class="demo-actions">
                    <a href="https://xiaoyu-edu.demo.com" class="action-btn btn-primary" target="_blank">进入课堂</a>
                    <a href="https://teacher.xiaoyu-edu.demo.com" class="action-btn btn-secondary" target="_blank">教师工作台</a>
                    <a href="#" class="action-btn btn-changelog" onclick="showStory('xiaoyu')">开发回忆</a>
                </div>
            </div> -->
        </div>
    </div>

    <script>
        // 项目故事数据
        const projectStories = {
            shop: {
                title: '跨境商城特色功能',
                content: `
                    <p>1、多种商品模式（国内现货、BC直邮、保税仓、CC直邮）</p>
                    <p>2、自定义商品税率</p>
                    <p>3、商品国家、商品发货地、预计到达天数设置</p>
                    <p>4、商品页跨境信息展示（国家、预计税费、预计到达日期）</p>
                    <p>5、跨境订单（BC直邮和保税仓）下单实名认证、智能计算订单税费</p>
                    <p>6、商城支持首页和新增页面拖拽式可视化装修</p>
                    <p>7、系统可直接对接海关进行报关或对接报关系统</p>
                    <p>8、更多功能请扫小程序码体验</p>
                `
            },
        };

        // 显示项目故事
        function showStory(projectKey) {
            const story = projectStories[projectKey];
            if (!story) return;

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.8);
                backdrop-filter: blur(10px);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: rgba(255,255,255,0.95);
                backdrop-filter: blur(20px);
                border-radius: 20px;
                padding: 30px;
                max-width: 500px;
                width: 100%;
                max-height: 80vh;
                overflow-y: auto;
                position: relative;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            `;

            content.innerHTML = `
                <h3 style="color: #333; margin-bottom: 20px; font-size: 1.5rem;">${story.title}</h3>
                <div style="color: #666; line-height: 1.8; font-size: 0.95rem;">${story.content}</div>
                <button onclick="this.closest('.modal').remove()" style="
                    position: absolute;
                    top: 15px;
                    right: 15px;
                    background: none;
                    border: none;
                    font-size: 1.5rem;
                    cursor: pointer;
                    color: #999;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                    transition: all 0.3s ease;
                " onmouseover="this.style.background='rgba(0,0,0,0.1)'" onmouseout="this.style.background='none'">×</button>
            `;

            modal.appendChild(content);
            modal.className = 'modal';
            document.body.appendChild(modal);

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }

        // 复制登录信息到剪贴板
        function copyLoginInfo(text) {
            navigator.clipboard.writeText(text).then(() => {
                showToast('账号信息已复制 📋');
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast('账号信息已复制 📋');
            });
        }

        // 显示提示消息
        function showToast(message) {
            const toastMessages = [
                message,
                message + ' 👍',
                message + ' ✨',
                message + ' 🎉'
            ];
            const randomMessage = toastMessages[Math.floor(Math.random() * toastMessages.length)];

            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%) scale(0.8);
                background: rgba(0,0,0,0.85);
                color: white;
                padding: 15px 25px;
                border-radius: 25px;
                z-index: 10000;
                font-size: 14px;
                backdrop-filter: blur(15px);
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            `;
            toast.textContent = randomMessage;
            document.body.appendChild(toast);

            // 动画进入
            setTimeout(() => {
                toast.style.transform = 'translate(-50%, -50%) scale(1)';
            }, 10);

            setTimeout(() => {
                toast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                toast.style.opacity = '0';
                setTimeout(() => toast.remove(), 300);
            }, 2000);
        }

        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 随机问候语
            const greetings = [
                '欢迎来到我们的作品展示 🎨',
                '这些项目都有温度哦 ❤️',
                '每个项目背后都有故事 📖',
                '看看我们的得意之作 ✨',
                '这些都是真实的项目 💯'
            ];

            setTimeout(() => {
                const randomGreeting = greetings[Math.floor(Math.random() * greetings.length)];
                showToast(randomGreeting);
            }, 1000);

            const cards = document.querySelectorAll('.demo-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'perspective(1000px) rotateX(5deg) rotateY(-2deg)';
                }, index * 150 + 500);
            });

            // 为登录信息添加点击复制功能
            document.querySelectorAll('.login-info').forEach(element => {
                element.style.cursor = 'pointer';
                element.title = '点击复制登录信息';
                element.addEventListener('click', () => {
                    copyLoginInfo(element.textContent.trim());
                });
            });

            // 添加导航栏滚动效果
            let lastScrollTop = 0;
            const navbar = document.querySelector('.nav-header');

            window.addEventListener('scroll', () => {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                if (scrollTop > lastScrollTop && scrollTop > 100) {
                    // 向下滚动，隐藏导航栏
                    navbar.style.transform = 'translateY(-100%)';
                } else {
                    // 向上滚动，显示导航栏
                    navbar.style.transform = 'translateY(0)';
                }

                lastScrollTop = scrollTop;
            });
        });
    </script>
</body>
</html>
