<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dark Projects - 暗黑模式项目展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'JetBrains Mono', 'Fira Code', monospace;
            background: #0d1117;
            color: #c9d1d9;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 60px 40px;
        }

        .header {
            text-align: center;
            margin-bottom: 80px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 2px;
            background: linear-gradient(90deg, #f78166, #58a6ff, #7c3aed);
            border-radius: 1px;
        }

        .header h1 {
            font-size: 3.5rem;
            font-weight: 700;
            color: #f0f6fc;
            margin-bottom: 20px;
            text-shadow: 0 0 20px rgba(88, 166, 255, 0.3);
        }

        .header p {
            font-size: 1.2rem;
            color: #8b949e;
            font-weight: 400;
        }

        .projects-container {
            display: grid;
            gap: 40px;
        }

        .project-row {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 40px;
            align-items: center;
            padding: 40px;
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 12px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .project-row::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, #f78166, #58a6ff, #7c3aed);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .project-row:hover::before {
            opacity: 1;
        }

        .project-row:hover {
            background: #1c2128;
            border-color: #58a6ff;
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .project-row:nth-child(even) {
            grid-template-columns: 2fr 1fr;
        }

        .project-row:nth-child(even) .project-info {
            order: 2;
        }

        .project-row:nth-child(even) .project-visual {
            order: 1;
        }

        .project-visual {
            background: #0d1117;
            border: 1px solid #21262d;
            border-radius: 8px;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .project-visual::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 30% 30%, rgba(88, 166, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, rgba(247, 129, 102, 0.1) 0%, transparent 50%);
        }

        .project-icon {
            font-size: 4rem;
            z-index: 1;
            position: relative;
        }

        .project-info h3 {
            font-size: 2rem;
            font-weight: 600;
            color: #f0f6fc;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .project-status {
            font-size: 0.8rem;
            padding: 4px 8px;
            border-radius: 12px;
            background: #238636;
            color: #fff;
            font-weight: 500;
        }

        .project-desc {
            font-size: 1rem;
            color: #8b949e;
            margin-bottom: 25px;
            line-height: 1.7;
        }

        .project-tech {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 25px;
        }

        .tech-tag {
            font-size: 0.8rem;
            padding: 4px 10px;
            background: #21262d;
            color: #58a6ff;
            border-radius: 16px;
            border: 1px solid #30363d;
        }

        .project-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 25px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: #0d1117;
            border: 1px solid #21262d;
            border-radius: 8px;
        }

        .stat-value {
            font-size: 1.3rem;
            font-weight: 700;
            color: #58a6ff;
            display: block;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #8b949e;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .project-actions {
            display: flex;
            gap: 15px;
        }

        .dark-btn {
            padding: 12px 24px;
            border: 1px solid #30363d;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            font-family: inherit;
        }

        .dark-btn-primary {
            background: #238636;
            color: #fff;
            border-color: #238636;
        }

        .dark-btn-primary:hover {
            background: #2ea043;
            border-color: #2ea043;
        }

        .dark-btn-secondary {
            background: transparent;
            color: #c9d1d9;
            border-color: #30363d;
        }

        .dark-btn-secondary:hover {
            background: #21262d;
            border-color: #58a6ff;
            color: #58a6ff;
        }

        .dark-btn-outline {
            background: transparent;
            color: #58a6ff;
            border-color: #58a6ff;
        }

        .dark-btn-outline:hover {
            background: #58a6ff;
            color: #0d1117;
        }

        .sidebar {
            position: fixed;
            right: 40px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 20px;
            z-index: 100;
        }

        .sidebar-item {
            width: 50px;
            height: 50px;
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #8b949e;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .sidebar-item:hover {
            background: #21262d;
            border-color: #58a6ff;
            color: #58a6ff;
            transform: translateX(-5px);
        }

        .code-block {
            background: #0d1117;
            border: 1px solid #21262d;
            border-radius: 6px;
            padding: 16px;
            margin: 20px 0;
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.85rem;
            color: #7d8590;
            position: relative;
        }

        .code-block::before {
            content: '// Live Demo';
            position: absolute;
            top: -10px;
            left: 16px;
            background: #0d1117;
            padding: 0 8px;
            font-size: 0.7rem;
            color: #58a6ff;
        }

        @media (max-width: 768px) {
            .container {
                padding: 40px 20px;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
            
            .project-row {
                grid-template-columns: 1fr !important;
                padding: 30px;
            }
            
            .project-row:nth-child(even) .project-info {
                order: 1;
            }
            
            .project-row:nth-child(even) .project-visual {
                order: 2;
            }
            
            .sidebar {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <a href="#" class="sidebar-item">🏠</a>
        <a href="#" class="sidebar-item">📊</a>
        <a href="#" class="sidebar-item">⚙️</a>
        <a href="#" class="sidebar-item">📧</a>
    </div>

    <div class="container">
        <header class="header">
            <h1>Dark Projects</h1>
            <p>// 专为夜猫子程序员设计的项目展示</p>
        </header>

        <div class="projects-container">
            <div class="project-row">
                <div class="project-visual">
                    <div class="project-icon">⚡</div>
                </div>
                <div class="project-info">
                    <h3>
                        Lightning Commerce
                        <span class="project-status">ACTIVE</span>
                    </h3>
                    <p class="project-desc">
                        高性能电商系统，专为深夜购物狂欢设计。支持秒杀、限时抢购等高并发场景，
                        让用户在黑夜中也能享受极速购物体验。
                    </p>
                    <div class="project-tech">
                        <span class="tech-tag">React</span>
                        <span class="tech-tag">Node.js</span>
                        <span class="tech-tag">Redis</span>
                        <span class="tech-tag">MongoDB</span>
                    </div>
                    <div class="project-stats">
                        <div class="stat-item">
                            <span class="stat-value">5.2K</span>
                            <span class="stat-label">Users</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">99.8%</span>
                            <span class="stat-label">Uptime</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">2ms</span>
                            <span class="stat-label">Response</span>
                        </div>
                    </div>
                    <div class="project-actions">
                        <a href="#" class="dark-btn dark-btn-primary">Live Demo</a>
                        <a href="#" class="dark-btn dark-btn-outline">Source Code</a>
                    </div>
                </div>
            </div>

            <div class="project-row">
                <div class="project-visual">
                    <div class="project-icon">🌙</div>
                </div>
                <div class="project-info">
                    <h3>
                        Night Office
                        <span class="project-status">BETA</span>
                    </h3>
                    <p class="project-desc">
                        为夜班工作者量身定制的办公系统。深色主题保护眼睛，智能提醒功能帮助
                        维持工作节奏，让深夜办公也能高效舒适。
                    </p>
                    <div class="project-tech">
                        <span class="tech-tag">Vue.js</span>
                        <span class="tech-tag">Express</span>
                        <span class="tech-tag">PostgreSQL</span>
                        <span class="tech-tag">Socket.io</span>
                    </div>
                    <div class="project-stats">
                        <div class="stat-item">
                            <span class="stat-value">280</span>
                            <span class="stat-label">Teams</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">94%</span>
                            <span class="stat-label">Satisfaction</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">24/7</span>
                            <span class="stat-label">Support</span>
                        </div>
                    </div>
                    <div class="project-actions">
                        <a href="#" class="dark-btn dark-btn-primary">Try Beta</a>
                        <a href="#" class="dark-btn dark-btn-secondary">Documentation</a>
                    </div>
                </div>
            </div>

            <div class="project-row">
                <div class="project-visual">
                    <div class="project-icon">🎯</div>
                </div>
                <div class="project-info">
                    <h3>
                        Focus Academy
                        <span class="project-status">STABLE</span>
                    </h3>
                    <p class="project-desc">
                        专注力训练平台，帮助学习者在数字化时代保持专注。采用番茄工作法和
                        游戏化设计，让学习变得更加有趣和高效。
                    </p>
                    <div class="project-tech">
                        <span class="tech-tag">Next.js</span>
                        <span class="tech-tag">TypeScript</span>
                        <span class="tech-tag">Prisma</span>
                        <span class="tech-tag">Tailwind</span>
                    </div>
                    <div class="project-stats">
                        <div class="stat-item">
                            <span class="stat-value">15.6K</span>
                            <span class="stat-label">Learners</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">96%</span>
                            <span class="stat-label">Completion</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">4.9</span>
                            <span class="stat-label">Rating</span>
                        </div>
                    </div>
                    <div class="project-actions">
                        <a href="#" class="dark-btn dark-btn-primary">Start Learning</a>
                        <a href="#" class="dark-btn dark-btn-outline">GitHub</a>
                    </div>
                </div>
            </div>

            <div class="project-row">
                <div class="project-visual">
                    <div class="project-icon">📡</div>
                </div>
                <div class="project-info">
                    <h3>
                        Data Terminal
                        <span class="project-status">ACTIVE</span>
                    </h3>
                    <p class="project-desc">
                        终端风格的数据分析平台，为数据科学家和分析师提供强大的可视化工具。
                        支持实时数据流处理和复杂查询操作。
                    </p>
                    <div class="project-tech">
                        <span class="tech-tag">Python</span>
                        <span class="tech-tag">FastAPI</span>
                        <span class="tech-tag">Apache Kafka</span>
                        <span class="tech-tag">D3.js</span>
                    </div>
                    <div class="code-block">
                        $ curl -X GET "https://api.dataterminal.dev/metrics"<br>
                        {"status": "online", "queries": 1247, "uptime": "99.9%"}
                    </div>
                    <div class="project-stats">
                        <div class="stat-item">
                            <span class="stat-value">3.8M</span>
                            <span class="stat-label">Records</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">99.9%</span>
                            <span class="stat-label">Accuracy</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">Real-time</span>
                            <span class="stat-label">Processing</span>
                        </div>
                    </div>
                    <div class="project-actions">
                        <a href="#" class="dark-btn dark-btn-primary">Access Terminal</a>
                        <a href="#" class="dark-btn dark-btn-secondary">API Docs</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 打字机效果
        function typeWriter(element, text, speed = 50) {
            let i = 0;
            element.innerHTML = '';
            function type() {
                if (i < text.length) {
                    element.innerHTML += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                }
            }
            type();
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const rows = document.querySelectorAll('.project-row');
            rows.forEach((row, index) => {
                row.style.opacity = '0';
                row.style.transform = 'translateY(30px)';
                row.style.transition = `all 0.6s ease ${index * 0.2}s`;
                
                setTimeout(() => {
                    row.style.opacity = '1';
                    row.style.transform = 'translateY(0)';
                }, index * 200);
            });

            // 代码块打字效果
            const codeBlock = document.querySelector('.code-block');
            const originalText = codeBlock.innerHTML;
            setTimeout(() => {
                typeWriter(codeBlock, originalText, 30);
            }, 2000);
        });

        // 按钮点击效果
        document.querySelectorAll('.dark-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
    </script>
</body>
</html>
