<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soft Projects - 新拟物化项目展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #e0e5ec;
            color: #4a5568;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 60px 40px;
        }

        .header {
            text-align: center;
            margin-bottom: 80px;
        }

        .header h1 {
            font-size: 3.5rem;
            font-weight: 300;
            color: #2d3748;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(255,255,255,0.8);
        }

        .header p {
            font-size: 1.2rem;
            color: #718096;
            font-weight: 300;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
        }

        .project-card {
            background: #e0e5ec;
            border-radius: 25px;
            padding: 40px;
            box-shadow: 
                20px 20px 40px #bebebe,
                -20px -20px 40px #ffffff;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 
                25px 25px 50px #bebebe,
                -25px -25px 50px #ffffff;
        }

        .project-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            background: #e0e5ec;
            box-shadow: 
                inset 8px 8px 16px #bebebe,
                inset -8px -8px 16px #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin-bottom: 25px;
        }

        .project-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
        }

        .project-desc {
            font-size: 1rem;
            line-height: 1.7;
            color: #718096;
            margin-bottom: 30px;
        }

        .project-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }

        .stat-box {
            background: #e0e5ec;
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            box-shadow: 
                inset 5px 5px 10px #bebebe,
                inset -5px -5px 10px #ffffff;
        }

        .stat-value {
            font-size: 1.3rem;
            font-weight: 700;
            color: #4299e1;
            display: block;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #a0aec0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .project-actions {
            display: flex;
            gap: 15px;
        }

        .neu-btn {
            flex: 1;
            padding: 15px 25px;
            border: none;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            font-family: inherit;
        }

        .neu-btn-primary {
            background: #4299e1;
            color: white;
            box-shadow: 
                8px 8px 16px #3182ce,
                -8px -8px 16px #63b3ed;
        }

        .neu-btn-primary:hover {
            background: #3182ce;
            transform: translateY(-2px);
        }

        .neu-btn-primary:active {
            transform: translateY(0);
            box-shadow: 
                4px 4px 8px #3182ce,
                -4px -4px 8px #63b3ed;
        }

        .neu-btn-secondary {
            background: #e0e5ec;
            color: #4a5568;
            box-shadow: 
                8px 8px 16px #bebebe,
                -8px -8px 16px #ffffff;
        }

        .neu-btn-secondary:hover {
            color: #2d3748;
            transform: translateY(-2px);
        }

        .neu-btn-secondary:active {
            transform: translateY(0);
            box-shadow: 
                inset 4px 4px 8px #bebebe,
                inset -4px -4px 8px #ffffff;
        }

        .floating-menu {
            position: fixed;
            top: 40px;
            right: 40px;
            display: flex;
            gap: 20px;
            z-index: 100;
        }

        .menu-item {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #e0e5ec;
            box-shadow: 
                8px 8px 16px #bebebe,
                -8px -8px 16px #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: #4a5568;
            transition: all 0.3s ease;
        }

        .menu-item:hover {
            transform: translateY(-3px);
            box-shadow: 
                10px 10px 20px #bebebe,
                -10px -10px 20px #ffffff;
        }

        .menu-item:active {
            transform: translateY(0);
            box-shadow: 
                inset 4px 4px 8px #bebebe,
                inset -4px -4px 8px #ffffff;
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(90deg, #4299e1, #63b3ed);
            border-radius: 0 2px 2px 0;
            transition: width 0.3s ease;
            z-index: 1000;
        }

        @media (max-width: 768px) {
            .container {
                padding: 40px 20px;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
            
            .projects-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .project-card {
                padding: 30px;
            }
            
            .floating-menu {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="progress-bar" id="progressBar"></div>
    
    <nav class="floating-menu">
        <a href="#" class="menu-item">🏠</a>
        <a href="#" class="menu-item">👤</a>
        <a href="#" class="menu-item">📧</a>
    </nav>

    <div class="container">
        <header class="header">
            <h1>Soft Projects</h1>
            <p>体验新拟物化设计的项目展示</p>
        </header>

        <div class="projects-grid">
            <div class="project-card">
                <div class="project-icon">🛒</div>
                <h3 class="project-title">柔和商城</h3>
                <p class="project-desc">采用新拟物化设计语言打造的电商平台，为用户提供舒适自然的购物体验，每个交互都经过精心设计。</p>
                <div class="project-stats">
                    <div class="stat-box">
                        <span class="stat-value">3.2K</span>
                        <span class="stat-label">用户</span>
                    </div>
                    <div class="stat-box">
                        <span class="stat-value">4.8</span>
                        <span class="stat-label">评分</span>
                    </div>
                    <div class="stat-box">
                        <span class="stat-value">99%</span>
                        <span class="stat-label">满意度</span>
                    </div>
                </div>
                <div class="project-actions">
                    <button class="neu-btn neu-btn-primary">体验</button>
                    <button class="neu-btn neu-btn-secondary">详情</button>
                </div>
            </div>

            <div class="project-card">
                <div class="project-icon">💼</div>
                <h3 class="project-title">云端办公</h3>
                <p class="project-desc">温润如玉的办公系统界面，让工作变得更加愉悦。每个按钮都像真实物体一样可以触摸感知。</p>
                <div class="project-stats">
                    <div class="stat-box">
                        <span class="stat-value">180</span>
                        <span class="stat-label">团队</span>
                    </div>
                    <div class="stat-box">
                        <span class="stat-value">92%</span>
                        <span class="stat-label">效率</span>
                    </div>
                    <div class="stat-box">
                        <span class="stat-value">24/7</span>
                        <span class="stat-label">在线</span>
                    </div>
                </div>
                <div class="project-actions">
                    <button class="neu-btn neu-btn-primary">进入</button>
                    <button class="neu-btn neu-btn-secondary">试用</button>
                </div>
            </div>

            <div class="project-card">
                <div class="project-icon">📚</div>
                <h3 class="project-title">智慧学堂</h3>
                <p class="project-desc">如纸质书本般温暖的在线教育平台，新拟物化的设计让学习回归本质，专注知识本身。</p>
                <div class="project-stats">
                    <div class="stat-box">
                        <span class="stat-value">9.1K</span>
                        <span class="stat-label">学生</span>
                    </div>
                    <div class="stat-box">
                        <span class="stat-value">96%</span>
                        <span class="stat-label">完成率</span>
                    </div>
                    <div class="stat-box">
                        <span class="stat-value">4.9</span>
                        <span class="stat-label">好评</span>
                    </div>
                </div>
                <div class="project-actions">
                    <button class="neu-btn neu-btn-primary">学习</button>
                    <button class="neu-btn neu-btn-secondary">课程</button>
                </div>
            </div>

            <div class="project-card">
                <div class="project-icon">📈</div>
                <h3 class="project-title">数据仪表</h3>
                <p class="project-desc">像真实仪表盘一样的数据展示界面，每个图表都有物理质感，让数据分析变得直观有趣。</p>
                <div class="project-stats">
                    <div class="stat-box">
                        <span class="stat-value">1.5M</span>
                        <span class="stat-label">数据点</span>
                    </div>
                    <div class="stat-box">
                        <span class="stat-value">99.8%</span>
                        <span class="stat-label">准确率</span>
                    </div>
                    <div class="stat-box">
                        <span class="stat-value">实时</span>
                        <span class="stat-label">更新</span>
                    </div>
                </div>
                <div class="project-actions">
                    <button class="neu-btn neu-btn-primary">查看</button>
                    <button class="neu-btn neu-btn-secondary">报告</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 滚动进度条
        window.addEventListener('scroll', () => {
            const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
            document.getElementById('progressBar').style.width = scrolled + '%';
        });

        // 按钮点击反馈
        document.querySelectorAll('.neu-btn, .menu-item').forEach(btn => {
            btn.addEventListener('mousedown', function() {
                this.style.transform = 'scale(0.98)';
            });
            
            btn.addEventListener('mouseup', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // 卡片进入动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        document.querySelectorAll('.project-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>
