<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retro Projects - 复古项目展示</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Press+Start+2P&family=VT323:wght@400&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'VT323', monospace;
            background: #2a1810;
            background-image: 
                radial-gradient(circle at 25% 25%, #3d2817 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, #4a3520 0%, transparent 50%),
                linear-gradient(45deg, #2a1810 25%, transparent 25%),
                linear-gradient(-45deg, #2a1810 25%, transparent 25%);
            background-size: 60px 60px, 60px 60px, 30px 30px, 30px 30px;
            color: #f4e4bc;
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                repeating-linear-gradient(
                    0deg,
                    transparent,
                    transparent 2px,
                    rgba(244, 228, 188, 0.03) 2px,
                    rgba(244, 228, 188, 0.03) 4px
                );
            pointer-events: none;
            z-index: 1;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 60px 40px;
            position: relative;
            z-index: 2;
        }

        .header {
            text-align: center;
            margin-bottom: 80px;
            position: relative;
        }

        .header::before {
            content: '★ ★ ★';
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            color: #d4af37;
            font-size: 1.5rem;
            letter-spacing: 20px;
        }

        .header h1 {
            font-family: 'Press Start 2P', cursive;
            font-size: 3rem;
            color: #d4af37;
            margin-bottom: 20px;
            text-shadow: 
                3px 3px 0px #8b4513,
                6px 6px 10px rgba(0,0,0,0.5);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 3px 3px 0px #8b4513, 6px 6px 10px rgba(0,0,0,0.5), 0 0 20px #d4af37; }
            to { text-shadow: 3px 3px 0px #8b4513, 6px 6px 10px rgba(0,0,0,0.5), 0 0 30px #d4af37, 0 0 40px #d4af37; }
        }

        .header p {
            font-size: 1.5rem;
            color: #cd853f;
            font-weight: 400;
        }

        .projects-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
        }

        .retro-card {
            background: linear-gradient(145deg, #3d2817, #2a1810);
            border: 3px solid #8b4513;
            border-radius: 15px;
            padding: 30px;
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 
                inset 0 0 0 2px #d4af37,
                0 8px 16px rgba(0,0,0,0.3);
        }

        .retro-card::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: linear-gradient(45deg, #d4af37, #8b4513, #d4af37);
            border-radius: 15px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .retro-card:hover::before {
            opacity: 1;
        }

        .retro-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 
                inset 0 0 0 2px #d4af37,
                0 15px 30px rgba(0,0,0,0.4);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px dashed #8b4513;
        }

        .project-badge {
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, #d4af37, #b8860b);
            border: 3px solid #8b4513;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            margin-right: 20px;
            box-shadow: inset 0 0 10px rgba(0,0,0,0.3);
        }

        .project-title {
            font-family: 'Press Start 2P', cursive;
            font-size: 1.2rem;
            color: #d4af37;
            text-shadow: 2px 2px 0px #8b4513;
        }

        .project-subtitle {
            font-size: 1rem;
            color: #cd853f;
            margin-top: 5px;
        }

        .project-description {
            font-size: 1.3rem;
            line-height: 1.6;
            color: #f4e4bc;
            margin-bottom: 25px;
            text-align: justify;
        }

        .vintage-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 25px;
        }

        .stat-display {
            background: #1a0f08;
            border: 2px solid #8b4513;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            position: relative;
        }

        .stat-display::before {
            content: '';
            position: absolute;
            top: 5px;
            right: 5px;
            width: 8px;
            height: 8px;
            background: #00ff00;
            border-radius: 50%;
            box-shadow: 0 0 10px #00ff00;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .stat-number {
            font-family: 'Press Start 2P', cursive;
            font-size: 1.1rem;
            color: #00ff00;
            display: block;
            text-shadow: 0 0 10px #00ff00;
        }

        .stat-text {
            font-size: 1rem;
            color: #cd853f;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .retro-buttons {
            display: flex;
            gap: 15px;
        }

        .arcade-btn {
            flex: 1;
            padding: 15px 20px;
            border: 3px solid #8b4513;
            border-radius: 10px;
            background: linear-gradient(145deg, #d4af37, #b8860b);
            color: #2a1810;
            font-family: 'Press Start 2P', cursive;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-transform: uppercase;
            position: relative;
            overflow: hidden;
        }

        .arcade-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .arcade-btn:hover::before {
            left: 100%;
        }

        .arcade-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.3);
            background: linear-gradient(145deg, #ffd700, #d4af37);
        }

        .arcade-btn:active {
            transform: translateY(0);
            box-shadow: inset 0 4px 8px rgba(0,0,0,0.3);
        }

        .vintage-nav {
            position: fixed;
            top: 30px;
            right: 30px;
            background: linear-gradient(145deg, #3d2817, #2a1810);
            border: 3px solid #8b4513;
            border-radius: 15px;
            padding: 20px;
            z-index: 100;
        }

        .nav-title {
            font-family: 'Press Start 2P', cursive;
            font-size: 0.8rem;
            color: #d4af37;
            text-align: center;
            margin-bottom: 15px;
        }

        .nav-links {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .nav-link {
            color: #cd853f;
            text-decoration: none;
            font-size: 1.1rem;
            padding: 8px 12px;
            border: 1px solid #8b4513;
            border-radius: 5px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background: #8b4513;
            color: #d4af37;
            transform: scale(1.05);
        }

        @media (max-width: 768px) {
            .container {
                padding: 40px 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .projects-showcase {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .retro-card {
                padding: 25px;
            }
            
            .vintage-nav {
                display: none;
            }
        }
    </style>
</head>
<body>
    <nav class="vintage-nav">
        <div class="nav-title">MENU</div>
        <div class="nav-links">
            <a href="#" class="nav-link">HOME</a>
            <a href="#" class="nav-link">ABOUT</a>
            <a href="#" class="nav-link">GAMES</a>
            <a href="#" class="nav-link">HELP</a>
        </div>
    </nav>

    <div class="container">
        <header class="header">
            <h1>RETRO ARCADE</h1>
            <p>~ 经典永不过时的项目展示厅 ~</p>
        </header>

        <div class="projects-showcase">
            <div class="retro-card">
                <div class="card-header">
                    <div class="project-badge">🕹️</div>
                    <div>
                        <div class="project-title">PIXEL SHOP</div>
                        <div class="project-subtitle">像素商城系统</div>
                    </div>
                </div>
                <p class="project-description">
                    重现80年代街机厅的购物体验！每个商品都是精心设计的像素艺术，
                    配合经典的8位音效，让购物变成一场怀旧的冒险游戏。
                </p>
                <div class="vintage-stats">
                    <div class="stat-display">
                        <span class="stat-number">8,BIT</span>
                        <span class="stat-text">USERS</span>
                    </div>
                    <div class="stat-display">
                        <span class="stat-number">99.9%</span>
                        <span class="stat-text">UPTIME</span>
                    </div>
                </div>
                <div class="retro-buttons">
                    <button class="arcade-btn">PLAY</button>
                    <button class="arcade-btn">INFO</button>
                </div>
            </div>

            <div class="retro-card">
                <div class="card-header">
                    <div class="project-badge">📼</div>
                    <div>
                        <div class="project-title">TAPE OFFICE</div>
                        <div class="project-subtitle">磁带办公系统</div>
                    </div>
                </div>
                <p class="project-description">
                    仿佛回到了使用打字机和传真机的年代！界面设计灵感来自老式办公设备，
                    让现代办公也能体验到复古的温暖感觉。
                </p>
                <div class="vintage-stats">
                    <div class="stat-display">
                        <span class="stat-number">420</span>
                        <span class="stat-text">OFFICES</span>
                    </div>
                    <div class="stat-display">
                        <span class="stat-number">RETRO</span>
                        <span class="stat-text">STYLE</span>
                    </div>
                </div>
                <div class="retro-buttons">
                    <button class="arcade-btn">START</button>
                    <button class="arcade-btn">DEMO</button>
                </div>
            </div>

            <div class="retro-card">
                <div class="card-header">
                    <div class="project-badge">📚</div>
                    <div>
                        <div class="project-title">BOOK CLUB</div>
                        <div class="project-subtitle">复古读书会</div>
                    </div>
                </div>
                <p class="project-description">
                    重现图书馆卡片目录系统的在线教育平台。每门课程都像一本珍贵的古籍，
                    学习进度用老式打卡机记录，让知识获取充满仪式感。
                </p>
                <div class="vintage-stats">
                    <div class="stat-display">
                        <span class="stat-number">12.3K</span>
                        <span class="stat-text">READERS</span>
                    </div>
                    <div class="stat-display">
                        <span class="stat-number">CLASSIC</span>
                        <span class="stat-text">BOOKS</span>
                    </div>
                </div>
                <div class="retro-buttons">
                    <button class="arcade-btn">READ</button>
                    <button class="arcade-btn">CATALOG</button>
                </div>
            </div>

            <div class="retro-card">
                <div class="card-header">
                    <div class="project-badge">📊</div>
                    <div>
                        <div class="project-title">DATA VAULT</div>
                        <div class="project-subtitle">数据保险库</div>
                    </div>
                </div>
                <p class="project-description">
                    模拟银行金库的数据分析系统！每个数据报表都需要"密码"解锁，
                    图表设计采用老式仪表盘风格，让数据分析变得神秘而有趣。
                </p>
                <div class="vintage-stats">
                    <div class="stat-display">
                        <span class="stat-number">2.8M</span>
                        <span class="stat-text">RECORDS</span>
                    </div>
                    <div class="stat-display">
                        <span class="stat-number">SECURE</span>
                        <span class="stat-text">VAULT</span>
                    </div>
                </div>
                <div class="retro-buttons">
                    <button class="arcade-btn">UNLOCK</button>
                    <button class="arcade-btn">REPORT</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 复古音效模拟
        function playRetroSound() {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);
            
            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.1);
        }

        // 按钮点击效果
        document.querySelectorAll('.arcade-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                playRetroSound();
                this.style.transform = 'translateY(2px)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-3px)';
                }, 100);
            });
        });

        // 卡片进入动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0) scale(1)';
                }
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.retro-card').forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px) scale(0.9)';
                card.style.transition = `all 0.8s ease ${index * 0.2}s`;
                observer.observe(card);
            });
        });

        // 随机闪烁效果
        setInterval(() => {
            const indicators = document.querySelectorAll('.stat-display::before');
            const randomCard = document.querySelectorAll('.retro-card')[Math.floor(Math.random() * 4)];
            randomCard.style.filter = 'brightness(1.2)';
            setTimeout(() => {
                randomCard.style.filter = 'brightness(1)';
            }, 200);
        }, 3000);
    </script>
</body>
</html>
