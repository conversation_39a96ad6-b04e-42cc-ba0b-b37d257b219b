<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Creative Canvas - 创意艺术项目展示</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Dancing+Script:wght@400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Playfair Display', serif;
            background: 
                radial-gradient(circle at 20% 80%, rgba(255, 182, 193, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(173, 216, 230, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 218, 185, 0.2) 0%, transparent 50%),
                linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #2c3e50;
            overflow-x: hidden;
        }

        .paint-strokes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .stroke {
            position: absolute;
            opacity: 0.1;
            animation: float 20s ease-in-out infinite;
        }

        .stroke:nth-child(1) {
            top: 10%;
            left: 5%;
            width: 200px;
            height: 20px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            border-radius: 10px;
            transform: rotate(-15deg);
            animation-delay: 0s;
        }

        .stroke:nth-child(2) {
            top: 60%;
            right: 10%;
            width: 150px;
            height: 15px;
            background: linear-gradient(45deg, #48dbfb, #0abde3);
            border-radius: 8px;
            transform: rotate(25deg);
            animation-delay: 5s;
        }

        .stroke:nth-child(3) {
            bottom: 20%;
            left: 15%;
            width: 180px;
            height: 18px;
            background: linear-gradient(45deg, #ff9ff3, #f368e0);
            border-radius: 9px;
            transform: rotate(-30deg);
            animation-delay: 10s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(var(--rotation, 0deg)); }
            50% { transform: translateY(-20px) rotate(calc(var(--rotation, 0deg) + 5deg)); }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 60px 40px;
            position: relative;
            z-index: 2;
        }

        .header {
            text-align: center;
            margin-bottom: 80px;
            position: relative;
        }

        .header::before {
            content: '✨';
            position: absolute;
            top: -40px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 2rem;
            animation: sparkle 2s ease-in-out infinite;
        }

        @keyframes sparkle {
            0%, 100% { transform: translateX(-50%) scale(1); }
            50% { transform: translateX(-50%) scale(1.2); }
        }

        .header h1 {
            font-family: 'Dancing Script', cursive;
            font-size: 4.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #f9ca24);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 4s ease infinite;
            margin-bottom: 20px;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .header p {
            font-size: 1.3rem;
            color: #7f8c8d;
            font-style: italic;
        }

        .gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 40px;
            margin-bottom: 60px;
        }

        .artwork-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 0;
            box-shadow: 
                0 10px 30px rgba(0, 0, 0, 0.1),
                0 1px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            overflow: hidden;
            position: relative;
            transform-style: preserve-3d;
        }

        .artwork-card:hover {
            transform: translateY(-15px) rotateX(5deg);
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.15),
                0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .card-canvas {
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow: hidden;
        }

        .card-canvas::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3) 0%, transparent 30%),
                radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.2) 0%, transparent 40%);
        }

        .canvas-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 4rem;
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        .card-content {
            padding: 30px;
        }

        .project-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
            position: relative;
        }

        .project-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 2px;
        }

        .project-medium {
            font-size: 0.9rem;
            color: #95a5a6;
            font-style: italic;
            margin-bottom: 15px;
        }

        .project-description {
            font-size: 1rem;
            line-height: 1.7;
            color: #34495e;
            margin-bottom: 25px;
        }

        .color-palette {
            display: flex;
            gap: 8px;
            margin-bottom: 25px;
        }

        .color-dot {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            transition: transform 0.2s ease;
        }

        .color-dot:hover {
            transform: scale(1.3);
        }

        .inspiration-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 25px;
        }

        .stat-bubble {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .stat-value {
            font-size: 1.4rem;
            font-weight: 700;
            color: #e74c3c;
            display: block;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .creative-actions {
            display: flex;
            gap: 12px;
        }

        .art-btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            position: relative;
            overflow: hidden;
        }

        .art-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .art-btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.6);
        }

        .floating-palette {
            position: fixed;
            top: 50%;
            right: 30px;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 15px;
            z-index: 100;
        }

        .palette-tool {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .palette-tool:nth-child(1) { background: linear-gradient(45deg, #ff6b6b, #ee5a52); }
        .palette-tool:nth-child(2) { background: linear-gradient(45deg, #4ecdc4, #44a08d); }
        .palette-tool:nth-child(3) { background: linear-gradient(45deg, #45b7d1, #3742fa); }
        .palette-tool:nth-child(4) { background: linear-gradient(45deg, #f9ca24, #f0932b); }

        .palette-tool:hover {
            transform: scale(1.2) rotate(10deg);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        @media (max-width: 768px) {
            .container {
                padding: 40px 20px;
            }
            
            .header h1 {
                font-size: 3rem;
            }
            
            .gallery {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .artwork-card {
                margin: 0 10px;
            }
            
            .floating-palette {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="paint-strokes">
        <div class="stroke" style="--rotation: -15deg;"></div>
        <div class="stroke" style="--rotation: 25deg;"></div>
        <div class="stroke" style="--rotation: -30deg;"></div>
    </div>

    <div class="floating-palette">
        <a href="#" class="palette-tool">🎨</a>
        <a href="#" class="palette-tool">✏️</a>
        <a href="#" class="palette-tool">🖌️</a>
        <a href="#" class="palette-tool">🎭</a>
    </div>

    <div class="container">
        <header class="header">
            <h1>Creative Canvas</h1>
            <p>~ 艺术与技术的完美融合 ~</p>
        </header>

        <div class="gallery">
            <div class="artwork-card">
                <div class="card-canvas" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="canvas-icon">🎨</div>
                </div>
                <div class="card-content">
                    <h3 class="project-title">彩虹商城</h3>
                    <p class="project-medium">数字水彩 · 交互艺术</p>
                    <p class="project-description">
                        如彩虹般绚烂的购物体验，每个商品都是一件艺术品。
                        界面采用水彩画风格，色彩流动自然，让购物变成一场视觉盛宴。
                    </p>
                    <div class="color-palette">
                        <div class="color-dot" style="background: #ff6b6b;"></div>
                        <div class="color-dot" style="background: #4ecdc4;"></div>
                        <div class="color-dot" style="background: #45b7d1;"></div>
                        <div class="color-dot" style="background: #f9ca24;"></div>
                        <div class="color-dot" style="background: #f0932b;"></div>
                    </div>
                    <div class="inspiration-stats">
                        <div class="stat-bubble">
                            <span class="stat-value">7.2K</span>
                            <span class="stat-label">艺术家</span>
                        </div>
                        <div class="stat-bubble">
                            <span class="stat-value">∞</span>
                            <span class="stat-label">创意</span>
                        </div>
                    </div>
                    <div class="creative-actions">
                        <button class="art-btn btn-primary">体验艺术</button>
                        <button class="art-btn btn-secondary">创作灵感</button>
                    </div>
                </div>
            </div>

            <div class="artwork-card">
                <div class="card-canvas" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);">
                    <div class="canvas-icon">🖼️</div>
                </div>
                <div class="card-content">
                    <h3 class="project-title">印象办公</h3>
                    <p class="project-medium">印象派 · 现代主义</p>
                    <p class="project-description">
                        受莫奈印象派启发的办公系统，界面如油画般温暖柔和。
                        每个功能模块都是一幅小画，让工作充满艺术气息。
                    </p>
                    <div class="color-palette">
                        <div class="color-dot" style="background: #ffecd2;"></div>
                        <div class="color-dot" style="background: #fcb69f;"></div>
                        <div class="color-dot" style="background: #ff8a80;"></div>
                        <div class="color-dot" style="background: #ffab91;"></div>
                        <div class="color-dot" style="background: #ffcc02;"></div>
                    </div>
                    <div class="inspiration-stats">
                        <div class="stat-bubble">
                            <span class="stat-value">450</span>
                            <span class="stat-label">工作室</span>
                        </div>
                        <div class="stat-bubble">
                            <span class="stat-value">98%</span>
                            <span class="stat-label">满意度</span>
                        </div>
                    </div>
                    <div class="creative-actions">
                        <button class="art-btn btn-primary">进入画廊</button>
                        <button class="art-btn btn-secondary">艺术工具</button>
                    </div>
                </div>
            </div>

            <div class="artwork-card">
                <div class="card-canvas" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                    <div class="canvas-icon">📚</div>
                </div>
                <div class="card-content">
                    <h3 class="project-title">梦幻学院</h3>
                    <p class="project-medium">超现实主义 · 梦境美学</p>
                    <p class="project-description">
                        如梦境般奇幻的学习空间，知识在这里变成了彩色的云朵。
                        每个课程都是一场奇妙的冒险，激发无限想象力。
                    </p>
                    <div class="color-palette">
                        <div class="color-dot" style="background: #a8edea;"></div>
                        <div class="color-dot" style="background: #fed6e3;"></div>
                        <div class="color-dot" style="background: #d299c2;"></div>
                        <div class="color-dot" style="background: #fef9d7;"></div>
                        <div class="color-dot" style="background: #b8e6b8;"></div>
                    </div>
                    <div class="inspiration-stats">
                        <div class="stat-bubble">
                            <span class="stat-value">25.8K</span>
                            <span class="stat-label">梦想家</span>
                        </div>
                        <div class="stat-bubble">
                            <span class="stat-value">∞</span>
                            <span class="stat-label">可能性</span>
                        </div>
                    </div>
                    <div class="creative-actions">
                        <button class="art-btn btn-primary">进入梦境</button>
                        <button class="art-btn btn-secondary">创造奇迹</button>
                    </div>
                </div>
            </div>

            <div class="artwork-card">
                <div class="card-canvas" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="canvas-icon">📊</div>
                </div>
                <div class="card-content">
                    <h3 class="project-title">数据雕塑</h3>
                    <p class="project-medium">抽象表现主义 · 数据艺术</p>
                    <p class="project-description">
                        将枯燥的数据转化为美丽的艺术作品。每个图表都是一件雕塑，
                        每个数字都有自己的颜色和形状，让分析变成艺术创作。
                    </p>
                    <div class="color-palette">
                        <div class="color-dot" style="background: #667eea;"></div>
                        <div class="color-dot" style="background: #764ba2;"></div>
                        <div class="color-dot" style="background: #f093fb;"></div>
                        <div class="color-dot" style="background: #f5576c;"></div>
                        <div class="color-dot" style="background: #4facfe;"></div>
                    </div>
                    <div class="inspiration-stats">
                        <div class="stat-bubble">
                            <span class="stat-value">8.9M</span>
                            <span class="stat-label">数据点</span>
                        </div>
                        <div class="stat-bubble">
                            <span class="stat-value">艺术</span>
                            <span class="stat-label">精度</span>
                        </div>
                    </div>
                    <div class="creative-actions">
                        <button class="art-btn btn-primary">雕刻数据</button>
                        <button class="art-btn btn-secondary">艺术报告</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 创意粒子效果
        function createArtParticles() {
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#f9ca24', '#f0932b'];
            const symbols = ['✨', '🎨', '🌈', '💫', '🎭'];
            
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.style.position = 'fixed';
                particle.style.left = Math.random() * 100 + 'vw';
                particle.style.top = Math.random() * 100 + 'vh';
                particle.style.fontSize = Math.random() * 20 + 10 + 'px';
                particle.style.color = colors[Math.floor(Math.random() * colors.length)];
                particle.style.pointerEvents = 'none';
                particle.style.zIndex = '1';
                particle.textContent = symbols[Math.floor(Math.random() * symbols.length)];
                particle.style.animation = `float ${Math.random() * 10 + 10}s ease-in-out infinite`;
                particle.style.animationDelay = Math.random() * 5 + 's';
                
                document.body.appendChild(particle);
                
                setTimeout(() => {
                    particle.remove();
                }, 15000);
            }
        }

        // 卡片3D效果
        document.querySelectorAll('.artwork-card').forEach(card => {
            card.addEventListener('mousemove', (e) => {
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                
                const rotateX = (y - centerY) / 10;
                const rotateY = (centerX - x) / 10;
                
                card.style.transform = `translateY(-15px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) rotateX(0) rotateY(0)';
            });
        });

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            createArtParticles();
            
            const cards = document.querySelectorAll('.artwork-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px) scale(0.8)';
                card.style.transition = `all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) ${index * 0.2}s`;
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0) scale(1)';
                }, index * 200);
            });
            
            // 定期创建新粒子
            setInterval(createArtParticles, 10000);
        });

        // 按钮点击效果
        document.querySelectorAll('.art-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
                
                // 创建点击爆炸效果
                const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#f9ca24'];
                for (let i = 0; i < 6; i++) {
                    const spark = document.createElement('div');
                    spark.style.position = 'absolute';
                    spark.style.width = '4px';
                    spark.style.height = '4px';
                    spark.style.background = colors[Math.floor(Math.random() * colors.length)];
                    spark.style.borderRadius = '50%';
                    spark.style.pointerEvents = 'none';
                    
                    const rect = this.getBoundingClientRect();
                    spark.style.left = rect.left + rect.width/2 + 'px';
                    spark.style.top = rect.top + rect.height/2 + 'px';
                    
                    document.body.appendChild(spark);
                    
                    const angle = (Math.PI * 2 * i) / 6;
                    const velocity = 50;
                    const vx = Math.cos(angle) * velocity;
                    const vy = Math.sin(angle) * velocity;
                    
                    spark.animate([
                        { transform: 'translate(0, 0) scale(1)', opacity: 1 },
                        { transform: `translate(${vx}px, ${vy}px) scale(0)`, opacity: 0 }
                    ], {
                        duration: 500,
                        easing: 'ease-out'
                    }).onfinish = () => spark.remove();
                }
            });
        });
    </script>
</body>
</html>
