<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高度差网络-项目展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #fafafa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 80px 40px;
        }

        .header {
            text-align: center;
            margin-bottom: 120px;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 300;
            color: #222;
            margin-bottom: 20px;
            letter-spacing: -1px;
        }

        .header p {
            font-size: 1.1rem;
            color: #666;
            font-weight: 300;
        }

        .projects {
            display: grid;
            gap: 80px;
        }

        .project {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 60px;
            align-items: center;
            padding: 60px 0;
            border-bottom: 1px solid #eee;
        }

        .project:last-child {
            border-bottom: none;
        }

        .project:nth-child(even) {
            grid-template-columns: 2fr 1fr;
        }

        .project:nth-child(even) .project-info {
            order: 2;
        }

        .project:nth-child(even) .project-visual {
            order: 1;
        }

        .project-visual {
            height: 300px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            transition: transform 0.3s ease;
        }

        .project-visual:hover {
            transform: translateY(-5px);
        }

        .project-info h3 {
            font-size: 2rem;
            font-weight: 400;
            color: #222;
            margin-bottom: 20px;
        }

        .project-info p {
            font-size: 1rem;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.8;
        }

        .project-meta {
            display: flex;
            gap: 40px;
            margin-bottom: 30px;
        }

        .meta-item {
            text-align: center;
        }

        .meta-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #222;
            display: block;
        }

        .meta-label {
            font-size: 0.8rem;
            color: #999;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .project-actions {
            display: flex;
            gap: 20px;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            background: none;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            font-family: inherit;
        }

        .btn-primary {
            background: #222;
            color: #fff;
            border-radius: 4px;
        }

        .btn-primary:hover {
            background: #000;
            transform: translateY(-2px);
        }

        .btn-secondary {
            color: #666;
            border: 1px solid #ddd;
            border-radius: 4px;
            position: relative;
        }

        .btn-secondary:hover {
            color: #222;
            border-color: #222;
        }

        /* 微信小程序二维码悬停效果 */
        .qr-tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            margin-bottom: 10px;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
            pointer-events: none;
        }

        .qr-tooltip img {
            width: 150px;
            height: 150px;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            background: white;
            padding: 8px;
        }

        .qr-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 8px solid transparent;
            border-top-color: white;
        }

        .btn-secondary:hover .qr-tooltip {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(-5px);
        }

        .floating-nav {
            position: fixed;
            top: 40px;
            right: 40px;
            display: flex;
            gap: 30px;
            z-index: 100;
        }

        .nav-item {
            color: #999;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .nav-item:hover {
            color: #222;
        }

        @media (max-width: 768px) {
            .container {
                padding: 40px 20px;
            }
            
            .header {
                margin-bottom: 60px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .project {
                grid-template-columns: 1fr !important;
                gap: 30px;
                padding: 40px 0;
            }
            
            .project:nth-child(even) .project-info {
                order: 1;
            }
            
            .project:nth-child(even) .project-visual {
                order: 2;
            }
            
            .project-meta {
                gap: 20px;
            }
            
            .floating-nav {
                display: none;
            }
        }
    </style>
</head>
<body>
    <nav class="floating-nav">
        <a href="https://www.gaodux.com" class="nav-item" target="_blank">访问官网</a>
        <a href="https://bbs.gaodux.com/" class="nav-item" target="_blank">访问论坛</a>
        <a href="https://bbs.gaodux.com/thread/48" class="nav-item" target="_blank">联系我们</a>
    </nav>

    <div class="container">
        <header class="header">
            <h1>项目展示</h1>
            <p>助力跨境进出口企业数字化转型升级</p>
        </header>

        <div class="projects">
            <div class="project">
                <div class="project-visual">
                    <img src="./images/1.jpg" alt="" style="width: 350px; border-radius: 20px;"></img>
                </div>
                <div class="project-info">
                    <h3>进口跨境电商系统</h3>
                    <p>智能零售+跨境电商商城系统，支持多种跨境贸易模式，提供一站式跨境解决方案，助力企业高效布局全球市场，打造私域流量增长新引擎。</p>
                    <div class="project-meta">
                        <div class="meta-item">
                            <span class="meta-value">v3.5.0</span>
                            <span class="meta-label">版本</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-value">365天</span>
                            <span class="meta-label">售后服务（可续）</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-value">定制化开发</span>
                            <span class="meta-label">其他支持服务</span>
                        </div>
                    </div>
                    <div class="project-actions">
                        <a href="https://www.gaodux.com/shop/" class="btn btn-primary" target="_blank">PC端演示</a>
                        <a href="javascript:void(0);" class="btn btn-secondary">
                            微信小程序
                            <div class="qr-tooltip">
                                <img src="images/wx.png" alt="微信小程序二维码">
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <div class="project">
                <div class="project-visual">
                    <img src="./images/2.png" style="width: 710px; border-radius: 20px;"></img>
                </div>
                <div class="project-info">
                    <h3>速通关-进口报关系统</h3>
                    <p>速通关进口报关系统是一款智能高效的报关管理平台，支持多海关、多渠道申报，无缝对接179号文及公服平台，满足各类进口业务需求。系统提供三单对碰、数据大屏监控、开放API接口及自动化定时任务，实现报关全流程数字化管理，提升通关效率，降低企业运营成本，确保合规性与数据准确性，助力企业轻松应对复杂通关业务。</p>
                    <div class="project-meta">
                        <div class="meta-item">
                            <span class="meta-value">v1.0.0</span>
                            <span class="meta-label">版本</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-value">2025年</span>
                            <span class="meta-label">开发时间</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-value">365天</span>
                            <span class="meta-label">售后服务（可续）</span>
                        </div>
                    </div>
                    <div class="project-actions">
                        <a href="https://stg.gaodux.com/admin" class="btn btn-primary" target="_blank">演示后台</a>
                        <a href="javascript:void(0);" class="btn btn-secondary">
                            登陆信息
                            <div class="qr-tooltip" style="background-color: #fff; width: 200px; padding: 20px; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.5); font-size: 14px;">
                                <span>账号：admin</span> <br/>
                                <span>密码：admin321</span>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <div class="project">
                <div class="project-visual">⚓</div>
                <div class="project-info">
                    <h3>出口报关系统</h3>
                    <p>跨境电商智能报关系统，支持多模式通关+全单证数字化申报，提供本地加签、批量导入、回执上报跟踪一站式解决方案，助力企业高效合规出海。</p>
                    <div class="project-meta">
                        <div class="meta-item">
                            <span class="meta-value">v2.1.0</span>
                            <span class="meta-label">版本</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-value">9610</span>
                            <span class="meta-label">出口报关</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-value">365天</span>
                            <span class="meta-label">售后服务（可续）</span>
                        </div>
                    </div>
                    <div class="project-actions">
                        <a href="https://9610.gaodux.com" class="btn btn-primary" target="_blank">演示后台</a>
                        <a href="javascript:void(0);" class="btn btn-secondary">
                            登陆信息
                            <div class="qr-tooltip" style="background-color: #fff; width: 200px; padding: 20px; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.5); font-size: 14px;">
                                <span>账号：admin</span> <br/>
                                <span>密码：admin321</span>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 平滑滚动效果
        document.addEventListener('DOMContentLoaded', function() {
            const projects = document.querySelectorAll('.project');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, { threshold: 0.1 });

            projects.forEach(project => {
                project.style.opacity = '0';
                project.style.transform = 'translateY(30px)';
                project.style.transition = 'all 0.6s ease';
                observer.observe(project);
            });
        });
    </script>
</body>
</html>
